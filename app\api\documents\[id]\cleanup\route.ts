import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Document Memory Cleanup API Endpoint
 * Handles cleaning up memories when documents are deleted
 */

/**
 * DELETE /api/documents/[id]/cleanup
 * Clean up all memories associated with a document
 */
export async function DELETE(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: documentId } = await params

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document ownership
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get document statistics before cleanup
    let cleanupStats = null
    try {
      cleanupStats = await memoryService.getSessionStatistics(documentId, session.user.id)
    } catch (error) {
      console.warn('Failed to get cleanup statistics:', error)
      cleanupStats = { message: 'Could not retrieve statistics' }
    }

    // Clean up document-specific memories
    const cleanupSuccess = await memoryService.deleteDocumentMemories(documentId, session.user.id)

    return NextResponse.json({
      success: cleanupSuccess,
      message: cleanupSuccess
        ? 'Document memories cleaned up successfully'
        : 'Some memories may not have been cleaned up',
      documentId,
      documentName: document.fileName,
      cleanupStats: cleanupStats || {
        message: 'No statistics available'
      }
    })

  } catch (error) {
    console.error('Document cleanup error:', error)

    return NextResponse.json(
      {
        error: 'Failed to cleanup document memories',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/documents/[id]/cleanup
 * Get cleanup preview information for a document
 */
export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: documentId } = await params

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document ownership
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get session information
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    // Get document statistics
    let stats = null
    try {
      stats = await memoryService.getSessionStatistics(documentId, session.user.id)

      if (!stats || stats.totalMemories === 0) {
        return NextResponse.json({
          hasMemories: false,
          message: 'No memories found for this document'
        })
      }
    } catch (error) {
      console.warn('Failed to get document statistics:', error)
      return NextResponse.json({
        hasMemories: false,
        message: 'Could not retrieve memory information'
      })
    }

    return NextResponse.json({
      hasMemories: true,
      documentName: document.fileName,
      sessionId: progress.sessionId,
      statistics: stats,
      preview: {
        totalMemories: stats?.totalMemories || 0,
        conversations: stats?.conversationCount || 0,
        learningProgress: stats?.learningProgressCount || 0,
        concepts: stats?.conceptsCount || 0,
        sessionDuration: stats?.sessionStarted && stats?.lastActivity
          ? new Date(stats.lastActivity).getTime() - new Date(stats.sessionStarted).getTime()
          : 0
      }
    })

  } catch (error) {
    console.error('Document cleanup preview error:', error)

    return NextResponse.json(
      {
        error: 'Failed to get cleanup preview',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}