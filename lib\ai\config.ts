import { AIProviderType } from './providers'

export interface AIConfig {
  provider: AIProviderType
  openai: {
    apiKey?: string
    model: string
    temperature: number
    maxTokens: number
  }
  anthropic: {
    apiKey?: string
    model: string
    temperature: number
    maxTokens: number
  }
  mem0: {
    apiKey?: string
  }
}

export function getAIConfig(): AIConfig {
  return {
    provider: (process.env.AI_PROVIDER as AIProviderType) || 'openai',
    openai: {
      apiKey: process.env.OPENAI_API_KEY,
      model: process.env.OPENAI_MODEL || 'gpt-4',
      temperature: parseFloat(process.env.OPENAI_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.OPENAI_MAX_TOKENS || '1000')
    },
    anthropic: {
      apiKey: process.env.ANTHROPIC_API_KEY,
      model: process.env.ANTHROPIC_MODEL || 'claude-3-sonnet-20240229',
      temperature: parseFloat(process.env.ANTHROPIC_TEMPERATURE || '0.7'),
      maxTokens: parseInt(process.env.ANTHROPIC_MAX_TOKENS || '1000')
    },
    mem0: {
      apiKey: process.env.MEM0_API_KEY
    }
  }
}

export function validateAIConfig(): { isValid: boolean; errors: string[] } {
  const config = getAIConfig()
  const errors: string[] = []

  // Validate selected provider
  if (!['openai', 'anthropic'].includes(config.provider)) {
    errors.push(`Invalid AI_PROVIDER: ${config.provider}. Must be 'openai' or 'anthropic'`)
  }

  // Validate OpenAI config if selected
  if (config.provider === 'openai') {
    if (!config.openai.apiKey) {
      errors.push('OPENAI_API_KEY is required when using OpenAI provider')
    }
  }

  // Validate Anthropic config if selected
  if (config.provider === 'anthropic') {
    if (!config.anthropic.apiKey) {
      errors.push('ANTHROPIC_API_KEY is required when using Anthropic provider')
    }
  }

  // Validate Mem0 config
  if (!config.mem0.apiKey) {
    errors.push('MEM0_API_KEY is required for memory functionality')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

export function logAIConfig(): void {
  const config = getAIConfig()
  const validation = validateAIConfig()

  console.log('🤖 AI Configuration:')
  console.log(`  Provider: ${config.provider}`)
  console.log(`  OpenAI Model: ${config.openai.model}`)
  console.log(`  Anthropic Model: ${config.anthropic.model}`)
  console.log(`  OpenAI API Key: ${config.openai.apiKey ? '✅ Set' : '❌ Missing'}`)
  console.log(`  Anthropic API Key: ${config.anthropic.apiKey ? '✅ Set' : '❌ Missing'}`)
  console.log(`  Mem0 API Key: ${config.mem0.apiKey ? '✅ Set' : '❌ Missing'}`)
  
  if (!validation.isValid) {
    console.log('❌ Configuration Errors:')
    validation.errors.forEach(error => console.log(`  - ${error}`))
  } else {
    console.log('✅ Configuration is valid')
  }
}
