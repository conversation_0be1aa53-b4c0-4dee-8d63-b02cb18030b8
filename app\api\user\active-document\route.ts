import { NextRequest, NextResponse } from 'next/server'
import { createClientWithRetry, withAuthRetry } from '@/lib/supabase/client-with-retry'
import { dbService } from '@/lib/db-service'
import { prisma } from '@/lib/prisma'

/**
 * GET /api/user/active-document
 * Get the user's currently active document for session persistence
 */
export async function GET(_request: NextRequest) {
    try {
        const supabase = await createClientWithRetry({ maxRetries: 2, baseDelay: 1000 })

        const { user, authError } = await withAuthRetry(async () => {
            const {
                data: { user },
                error: authError,
            } = await supabase.auth.getUser()

            return { user, authError }
        }, 'active document auth')

        if (authError || !user) {
            console.warn('Active document auth failed:', authError)
            return NextResponse.json({
                error: 'Authentication failed',
                details: authError?.message || 'User not authenticated',
                retryable: true
            }, { status: 401 })
        }

        // Get the most recently updated document with progress
        const recentProgress = await prisma.progress.findFirst({
            where: {
                userId: user.id
            },
            include: {
                document: true
            },
            orderBy: {
                updatedAt: 'desc'
            }
        })

        if (!recentProgress || recentProgress.document.status !== 'READY') {
            return NextResponse.json({
                hasActiveDocument: false,
                message: 'No active document found'
            })
        }

        return NextResponse.json({
            hasActiveDocument: true,
            document: {
                id: recentProgress.document.id,
                name: recentProgress.document.fileName,
                currentChunk: recentProgress.currentChunk,
                totalChunks: recentProgress.document.totalChunks,
                sessionId: recentProgress.sessionId,
                hasExistingSession: !!recentProgress.sessionId
            }
        })

    } catch (error) {
        console.error('Active document API error:', error)
        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        )
    }
}

/**
 * POST /api/user/active-document
 * Set the user's currently active document
 */
export async function POST(request: NextRequest) {
    try {
        const supabase = await createClientWithRetry({ maxRetries: 2, baseDelay: 1000 })

        const { user, authError } = await withAuthRetry(async () => {
            const {
                data: { user },
                error: authError,
            } = await supabase.auth.getUser()

            return { user, authError }
        }, 'set active document auth')

        if (authError || !user) {
            console.warn('Set active document auth failed:', authError)
            return NextResponse.json({
                error: 'Authentication failed',
                details: authError?.message || 'User not authenticated',
                retryable: true
            }, { status: 401 })
        }

        const { documentId } = await request.json()

        if (!documentId) {
            return NextResponse.json(
                { error: 'Document ID is required' },
                { status: 400 }
            )
        }

        // Verify document exists and belongs to user
        const document = await dbService.document.findFirst({
            where: {
                id: documentId,
                userId: user.id
            }
        })

        if (!document) {
            return NextResponse.json(
                { error: 'Document not found or access denied' },
                { status: 404 }
            )
        }

        // Update the progress record's updatedAt to mark it as most recent
        await dbService.progress.upsert({
            where: {
                userId_documentId: {
                    userId: user.id,
                    documentId
                }
            },
            update: {
                updatedAt: new Date()
            },
            create: {
                userId: user.id,
                documentId,
                currentChunk: 0,
                sessionId: null
            }
        })

        return NextResponse.json({
            success: true,
            message: 'Active document updated'
        })

    } catch (error) {
        console.error('Set active document API error:', error)
        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        )
    }
}