# Rate Limiting Setup

## Overview

The security service now supports both in-memory (development) and distributed (production) rate limiting to handle multi-server deployments.

## Development Setup

By default, the system uses in-memory rate limiting which works fine for development and single-server deployments.

```typescript
// No additional setup needed - works out of the box
import { withRateLimit, SecurityService } from '@/lib/security-service'

const rateLimitResult = await withRateLimit(SecurityService.RATE_LIMITS.UPLOAD)(request)
```

## Production Setup

For production environments with multiple servers, configure Redis-based rate limiting:

### Option 1: Standard Redis

1. **Install Redis package:**
   ```bash
   npm install redis
   ```

2. **Set environment variable:**
   ```env
   REDIS_URL=redis://localhost:6379
   # or for Redis Cloud/AWS ElastiCache:
   REDIS_URL=redis://username:password@host:port
   ```

3. **Initialize in your app:**
   ```typescript
   // In your app initialization (e.g., middleware.ts or _app.tsx)
   import { configureRateLimit } from '@/lib/rate-limit-config'
   
   await configureRateLimit()
   ```

### Option 2: Upstash Redis (Recommended for Serverless)

1. **Install Upstash package:**
   ```bash
   npm install @upstash/redis
   ```

2. **Set environment variables:**
   ```env
   UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
   UPSTASH_REDIS_REST_TOKEN=your-token
   ```

3. **Initialize in your app:**
   ```typescript
   import { configureRateLimit } from '@/lib/rate-limit-config'
   
   await configureRateLimit()
   ```

## Usage Examples

### Basic Rate Limiting

```typescript
import { withRateLimit, SecurityService } from '@/lib/security-service'

export async function POST(request: NextRequest) {
  // Check rate limit
  const rateLimitResult = await withRateLimit(SecurityService.RATE_LIMITS.UPLOAD)(request)
  
  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      { error: rateLimitResult.error },
      { status: 429 }
    )
  }
  
  // Process request...
}
```

### Authenticated Rate Limiting

```typescript
import { withAuthenticatedRateLimit, SecurityService } from '@/lib/security-service'

export async function POST(request: NextRequest) {
  const userId = await getUserId(request) // Your auth logic
  
  // Check both IP and user-based rate limits
  const rateLimitResult = await withAuthenticatedRateLimit(
    SecurityService.RATE_LIMITS.PROCESSING,
    userId
  )(request)
  
  if (!rateLimitResult.allowed) {
    return NextResponse.json(
      { error: rateLimitResult.error },
      { status: 429 }
    )
  }
  
  // Process request...
}
```

## Configuration Options

### Rate Limit Presets

```typescript
SecurityService.RATE_LIMITS = {
  UPLOAD: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 5          // 5 uploads per minute
  },
  PROCESSING: {
    windowMs: 60 * 1000,    // 1 minute  
    maxRequests: 10         // 10 processing requests per minute
  },
  CHAT: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 30         // 30 chat messages per minute
  },
  GENERAL: {
    windowMs: 60 * 1000,    // 1 minute
    maxRequests: 100        // 100 general requests per minute
  }
}
```

### Custom Rate Limit Configuration

```typescript
const customConfig = {
  windowMs: 5 * 60 * 1000,  // 5 minutes
  maxRequests: 20,          // 20 requests per 5 minutes
  keyGenerator: (req) => {
    // Custom key generation logic
    return `custom:${req.ip}:${req.headers.get('user-agent')}`
  }
}

const result = await withRateLimit(customConfig)(request)
```

## Deployment Considerations

### Vercel/Netlify (Serverless)
- Use **Upstash Redis** for serverless compatibility
- Redis connections are automatically managed
- No persistent connections needed

### Traditional Hosting (VPS/Docker)
- Use **standard Redis** for better performance
- Consider Redis clustering for high availability
- Monitor Redis memory usage

### Environment Variables

```env
# Development
NODE_ENV=development

# Production with standard Redis
NODE_ENV=production
REDIS_URL=redis://localhost:6379

# Production with Upstash Redis (serverless)
NODE_ENV=production
UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
UPSTASH_REDIS_REST_TOKEN=your-token
```

## Monitoring

The system automatically logs rate limiting configuration:

```
✅ Rate limiting configured with Redis
📝 Rate limiting using in-memory store (development mode)
⚠️ Failed to configure Redis rate limiting, falling back to in-memory
```

Monitor these logs to ensure proper configuration in production.