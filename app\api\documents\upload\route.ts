import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"
import { uploadFileToSupabase } from "@/lib/supabase/storage"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler"

// Background processing function
async function processDocumentInBackground(documentId: string, buffer: Buffer, fileName: string, userId: string) {
  console.log(`[${documentId}] Starting background processing for ${fileName} (${buffer.length} bytes)`)

  try {
    // Check environment variables first
    if (!process.env.LLAMA_CLOUD_API_KEY) {
      throw new Error('LLAMA_CLOUD_API_KEY environment variable is not configured')
    }

    console.log(`[${documentId}] Environment check passed, importing LlamaParse service...`)
    const { llamaParseService } = await import("@/lib/llamaparse-service")

    console.log(`[${documentId}] Starting LlamaParse processing for document ${documentId} (${fileName})`)

    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('LlamaParse processing timed out after 5 minutes')), 5 * 60 * 1000)
    })

    // Parse document with timeout
    const parsedDocuments = await Promise.race([
      llamaParseService.parseDocumentFromBuffer(buffer, fileName),
      timeoutPromise
    ]) as any[]

    if (!parsedDocuments || parsedDocuments.length === 0) {
      console.log(`[${documentId}] No content extracted from document`)
      await dbService.document.update({
        where: { id: documentId },
        data: {
          status: 'ERROR',
          errorMessage: 'No content could be extracted from the document'
        }
      })
      return
    }

    console.log(`[${documentId}] Document successfully parsed into ${parsedDocuments.length} chunks`)

    // Store chunks in database
    console.log(`[${documentId}] Clearing existing chunks...`)
    await dbService.chunk.deleteMany({ where: { documentId } })

    console.log(`[${documentId}] Storing ${parsedDocuments.length} chunks in database...`)
    for (let i = 0; i < parsedDocuments.length; i++) {
      const chunk = parsedDocuments[i]
      const pageNumber = chunk.metadata.page_number || null

      await dbService.chunk.create({
        data: {
          documentId,
          chunkIndex: i,
          pageNumber,
          content: chunk.text,
          userId: userId
        }
      })
    }

    // Update document status to READY
    await dbService.document.update({
      where: { id: documentId },
      data: {
        status: 'READY',
        totalChunks: parsedDocuments.length,
        errorMessage: null
      }
    })

    console.log(`[${documentId}] Successfully processed document ${documentId}`)

    // Document processing complete - AI will be initialized when user opens chat interface

  } catch (error) {
    console.error(`[${documentId}] Background processing failed:`, error)

    // Use comprehensive error handling
    await ErrorHandler.handleDocumentError(documentId, error, userId)
  }
}

export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()
    const file = formData.get("file") as File

    if (!file) {
      return NextResponse.json({ error: "No file provided" }, { status: 400 })
    }

    // Validate file type - support LlamaParse formats
    const allowedTypes = [
      "application/pdf",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
      "application/msword", // .doc
      "application/vnd.openxmlformats-officedocument.presentationml.presentation", // .pptx
      "application/vnd.ms-powerpoint", // .ppt
      "text/plain", // .txt
      "text/markdown", // .md
      "application/rtf", // .rtf
      "text/html", // .html
      "text/htm", // .htm
    ]

    // Also validate by file extension as MIME types can be inconsistent
    const lowerFileName = file.name.toLowerCase()
    const supportedExtensions = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.txt', '.md', '.rtf', '.html', '.htm']
    const fileExtension = lowerFileName.substring(lowerFileName.lastIndexOf('.'))

    const isValidMimeType = allowedTypes.includes(file.type)
    const isValidExtension = supportedExtensions.includes(fileExtension)

    if (!isValidMimeType && !isValidExtension) {
      return NextResponse.json(
        { error: "Invalid file type. Please upload PDF, Word, PowerPoint, Text, Markdown, RTF, or HTML files." },
        { status: 400 },
      )
    }

    // Validate file size (50MB)
    if (file.size > 50 * 1024 * 1024) {
      return NextResponse.json({ error: "File too large. Maximum size is 50MB." }, { status: 400 })
    }

    // Sanitize filename to remove special characters that cause Supabase issues
    const sanitizedName = file.name
      .replace(/[™®©]/g, '') // Remove trademark symbols
      .replace(/[^a-zA-Z0-9.\-_]/g, '_') // Keep only alphanumeric, dots, hyphens, underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_+|_+$/g, '') // Remove leading/trailing underscores

    const fileName = `${Date.now()}-${sanitizedName}`
    console.log(`Original filename: ${file.name}`)
    console.log(`Sanitized filename: ${fileName}`)

    // Check if user exists and create if not
    let user = await dbService.user.findUnique({ id: session.user.id })
    if (!user) {
      console.log(`Creating new user with ID: ${session.user.id}`)
      user = await dbService.user.create({
        id: session.user.id,
        email: session.user.email || '<EMAIL>'
      })
    }

    // Create document record
    const document = await dbService.document.create({
      data: {
        userId: session.user.id,
        fileName: file.name,
        filePath: fileName,
        totalChunks: 0,
        status: 'PROCESSING',
        errorMessage: null,
      },
    })

    // Upload file to Supabase storage using authenticated client
    const { error: uploadError } = await uploadFileToSupabase(fileName, file, session.user.id, supabase)

    if (uploadError) {
      await dbService.document.delete({ where: { id: document.id } })
      throw new Error(`Failed to upload file: ${uploadError}`)
    }

    // Trigger document processing in the background
    try {
      console.log(`Starting direct processing for document ${document.id}`)

      // Retrieve file from Supabase Storage first
      console.log(`Downloading file from storage: ${session.user.id}/${fileName}`)
      const { data: fileData, error: downloadError } = await supabase.storage
        .from('documents')
        .download(`${session.user.id}/${fileName}`)

      if (downloadError || !fileData) {
        console.error(`Failed to retrieve file for processing: ${downloadError?.message}`)
        await dbService.document.update({
          where: { id: document.id },
          data: {
            status: 'ERROR',
            errorMessage: `Failed to retrieve file: ${downloadError?.message || 'File not found'}`
          }
        })
      } else {
        console.log(`File downloaded successfully, size: ${fileData.size} bytes`)

        // Convert Blob to Buffer for LlamaParse
        const arrayBuffer = await fileData.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)

        console.log(`Buffer created, size: ${buffer.length} bytes`)

        // Process in background (don't await to avoid timeout)
        // Use setImmediate to ensure it runs asynchronously
        setImmediate(() => {
          processDocumentInBackground(document.id, buffer, file.name, session.user.id)
            .catch(error => {
              console.error(`Background processing promise rejected:`, error)
            })
        })

        console.log(`Background processing initiated for document ${document.id}`)
      }
    } catch (processError) {
      console.error(`Failed to start document processing:`, processError)
      await ErrorHandler.handleDocumentError(document.id, processError, session.user.id)
    }

    return NextResponse.json({
      success: true,
      documentId: document.id,
      message: "Document uploaded successfully and processing started",
      status: 'PROCESSING'
    })
  } catch (error) {
    console.error("Upload error:", error)

    const processedError = ErrorHandler.categorizeError(error)

    return NextResponse.json(
      {
        error: processedError.userMessage,
        details: processedError.message,
        retryable: processedError.retryable,
        code: processedError.code
      },
      { status: 500 },
    )
  }
}

