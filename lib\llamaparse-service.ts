import { LlamaParseReader } from 'llamaindex';
import { config } from 'dotenv';
import fs from 'fs';
import path from 'path';
import os from 'os';

// Load environment variables
config();

/**
 * LlamaParse document structure returned by the API
 */
export interface LlamaParseDocument {
    id_: string;
    text: string;
    metadata: {
        file_path: string;
        [key: string]: any;
    };
}

/**
 * LlamaParse service for parsing documents into structured chunks
 */
export class LlamaParseService {
    private reader: LlamaParseReader;

    constructor() {
        if (!process.env.LLAMA_CLOUD_API_KEY) {
            throw new Error('LLAMA_CLOUD_API_KEY environment variable is required');
        }

        this.reader = new LlamaParseReader({
            resultType: 'markdown', // Output format as markdown
            apiKey: process.env.LLAMA_CLOUD_API_KEY
        });
    }

    /**
     * Parse a document from file path and return structured documents/chunks
     * @param filePath - Path to the file to parse
     * @returns Promise<LlamaParseDocument[]> - Array of parsed document chunks
     */
    async parseDocument(filePath: string): Promise<LlamaParseDocument[]> {
        try {
            const documents = await this.reader.loadData(filePath);
            // Transform the documents to match our interface
            return documents.map((doc: any) => ({
                id_: doc.id_ || doc.id || '',
                text: doc.text || '',
                metadata: {
                    file_path: filePath,
                    ...doc.metadata
                }
            }));
        } catch (error) {
            console.error('LlamaParse parsing error:', error);
            throw new Error(`Failed to parse document: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Parse a document from buffer and return structured documents/chunks
     * @param buffer - File buffer to parse
     * @param fileName - Original file name for metadata
     * @returns Promise<LlamaParseDocument[]> - Array of parsed document chunks
     */
    async parseDocumentFromBuffer(buffer: Buffer, fileName: string): Promise<LlamaParseDocument[]> {
        const startTime = Date.now();
        console.log(`Starting LlamaParse processing for file: ${fileName} (${buffer.length} bytes)`);

        // Validate file type before processing
        if (!LlamaParseService.isSupportedFileType(fileName)) {
            const supportedTypes = LlamaParseService.getSupportedFileTypes().join(', ');
            throw new Error(`Unsupported file type. Supported formats: ${supportedTypes}`);
        }

        // Validate buffer size (not empty, reasonable size)
        if (buffer.length === 0) {
            throw new Error('File buffer is empty');
        }

        if (buffer.length > 50 * 1024 * 1024) { // 50MB limit
            throw new Error('File too large for processing (maximum 50MB)');
        }

        let tempFilePath: string | null = null;

        try {
            // Create a temporary file from buffer for LlamaParseReader

            const tempDir = os.tmpdir();
            tempFilePath = path.join(tempDir, `llamaparse_${Date.now()}_${fileName}`);

            console.log(`Creating temporary file: ${tempFilePath}`);

            // Write buffer to temporary file
            fs.writeFileSync(tempFilePath, buffer);

            console.log(`Temporary file created successfully, calling LlamaParse API...`);

            try {
                const documents = await this.reader.loadData(tempFilePath!);

                const processingTime = Date.now() - startTime;
                console.log(`LlamaParse API call completed in ${processingTime}ms, received ${documents.length} documents`);

                // Validate response
                if (!Array.isArray(documents)) {
                    throw new Error('LlamaParse returned invalid response format');
                }

                // Transform documents and add filename to metadata
                const transformedDocuments = documents.map((doc: any, index: number) => {
                    const text = doc.text || '';
                    const docId = doc.id_ || doc.id || `chunk_${index}`;

                    console.log(`Processing chunk ${index}: ID=${docId}, text length=${text.length}`);

                    return {
                        id_: docId,
                        text: text,
                        metadata: {
                            file_path: fileName,
                            file_name: fileName,
                            chunk_index: index,
                            processing_time_ms: processingTime,
                            ...doc.metadata
                        }
                    };
                });

                console.log(`Successfully processed ${fileName} into ${transformedDocuments.length} chunks in ${processingTime}ms`);
                return transformedDocuments;

            } finally {
                // Clean up temporary file
                if (tempFilePath && fs.existsSync(tempFilePath)) {
                    console.log(`Cleaning up temporary file: ${tempFilePath}`);
                    fs.unlinkSync(tempFilePath);
                }
            }
        } catch (error) {
            const processingTime = Date.now() - startTime;
            console.error(`LlamaParse processing failed for ${fileName} after ${processingTime}ms:`, error);

            // Enhanced error messages based on error type
            if (error instanceof Error) {
                if (error.message.includes('API key')) {
                    throw new Error('LlamaParse API key is invalid or missing');
                } else if (error.message.includes('rate limit')) {
                    throw new Error('LlamaParse API rate limit exceeded. Please try again later');
                } else if (error.message.includes('timeout')) {
                    throw new Error('LlamaParse processing timed out. The file may be too complex');
                } else if (error.message.includes('unsupported')) {
                    throw new Error(`File format not supported by LlamaParse: ${fileName}`);
                } else {
                    throw new Error(`LlamaParse processing failed: ${error.message}`);
                }
            }

            throw new Error(`Failed to parse document from buffer: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
    }

    /**
     * Validate if a file type is supported by LlamaParse
     * @param fileName - Name of the file to validate
     * @returns boolean - True if file type is supported
     */
    static isSupportedFileType(fileName: string): boolean {
        const supportedExtensions = [
            '.pdf',
            '.docx',
            '.doc',
            '.pptx',
            '.ppt',
            '.txt',
            '.md',
            '.rtf',
            '.html',
            '.htm'
        ];

        const extension = fileName.toLowerCase().substring(fileName.lastIndexOf('.'));
        return supportedExtensions.includes(extension);
    }

    /**
     * Get supported file types for display in UI
     * @returns string[] - Array of supported file extensions
     */
    static getSupportedFileTypes(): string[] {
        return [
            'PDF (.pdf)',
            'Word Documents (.docx, .doc)',
            'PowerPoint (.pptx, .ppt)',
            'Text Files (.txt)',
            'Markdown (.md)',
            'Rich Text (.rtf)',
            'HTML (.html, .htm)'
        ];
    }
}

// Export a singleton instance for reuse
export const llamaParseService = new LlamaParseService();
