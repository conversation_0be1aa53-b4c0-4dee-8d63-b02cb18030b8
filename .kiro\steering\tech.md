# Technology Stack

## Framework & Runtime
- **Next.js 15.2.4** with App Router
- **React 19** with TypeScript
- **Node.js** (Latest LTS)
- **pnpm** as package manager

## Database & Backend
- **Supabase** for authentication and database management
- **Prisma** as ORM with PostgreSQL
- **Database Architecture**: Supabase Auth + Custom public schema tables
- **Automatic triggers** for user creation/deletion across schemas

## Styling & UI
- **Tailwind CSS** for styling with custom design system
- **Radix UI** components for accessible primitives
- **shadcn/ui** component library (configured in components.json)
- **Lucide React** for icons
- **next-themes** for dark/light mode support
- **Custom CSS variables** for theming with HSL color system

## Forms & Validation
- **React Hook Form** for form management
- **Zod** for schema validation and type safety
- **@hookform/resolvers** for Zod integration

## Development Tools
- **TypeScript 5** with strict mode enabled
- **ESLint** with Next.js configuration
- **Husky** for git hooks
- **PostCSS** for CSS processing

## Common Commands

### Development
```bash
pnpm dev          # Start development server
pnpm build        # Build for production
pnpm start        # Start production server
pnpm lint         # Run ESLint
```

### Database
```bash
npx prisma generate    # Generate Prisma client
npx prisma db push     # Push schema changes to database
npx prisma studio      # Open Prisma Studio
```

### Dependencies
```bash
pnpm install          # Install dependencies
pnpm add <package>    # Add new dependency
pnpm add -D <package> # Add dev dependency
```

## Key Libraries
- **@supabase/ssr** - Server-side rendering support
- **class-variance-authority** - Component variant management
- **clsx** + **tailwind-merge** - Conditional CSS classes
- **date-fns** - Date manipulation
- **sonner** - Toast notifications
- **recharts** - Data visualization