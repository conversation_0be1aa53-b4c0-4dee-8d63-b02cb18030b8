"use client"
import React from "react"
import { createClient } from "@/lib/supabase/client"
import { SidebarProvider } from "@/components/ui/sidebar"
import { AppSidebar } from "@/components/app-sidebar"
import { useState, useEffect } from "react"
import { SidebarContextProvider, useSidebarContext } from "@/components/sidebar-context"
import { MobileSidebar } from "@/components/mobile-sidebar"

function DashboardContent({ children, user }: { children: React.ReactNode; user: any }) {
  const { sidebarVisible, setSidebarVisible, isMobile } = useSidebarContext()

  return (
    <SidebarProvider>
      <div className="flex h-screen w-full relative">
        {/* Desktop: Normal sidebar behavior */}
        {!isMobile && sidebarVisible && (
          <div className="h-screen">
            <AppSidebar user={user} />
          </div>
        )}
        
        {/* Mobile: Overlay sidebar - render inside SidebarProvider context */}
        <main className="flex-1 h-full overflow-hidden">{children}</main>
        
        {/* Mobile sidebar rendered inside the provider */}
        {isMobile && sidebarVisible && (
          <>
            {/* Backdrop */}
            <div 
              className="fixed inset-0 bg-black bg-opacity-50 z-40"
              onClick={() => setSidebarVisible(false)}
            />
            {/* Mobile Sidebar - Using custom component */}
            <div className="fixed left-0 top-0 h-screen z-50">
              <MobileSidebar user={user} />
            </div>
          </>
        )}
      </div>
    </SidebarProvider>
  )
}

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [user, setUser] = useState<any>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const checkAuth = async () => {
      const supabase = createClient()
      const {
        data: { session },
      } = await supabase.auth.getSession()

      if (!session) {
        window.location.href = "/auth"
        return
      }

      setUser(session.user)
      setLoading(false)
    }

    checkAuth()
  }, [])

  if (loading) {
    return <div className="flex items-center justify-center h-screen">Loading...</div>
  }

  if (!user) {
    return null
  }

  return (
    <SidebarContextProvider>
      <DashboardContent user={user}>{children}</DashboardContent>
    </SidebarContextProvider>
  )
}
