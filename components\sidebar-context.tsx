"use client"
import React, { createContext, useContext, useState, ReactNode, useEffect } from "react"

interface SidebarContextType {
  sidebarVisible: boolean
  setSidebarVisible: (visible: boolean) => void
  isMobile: boolean
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined)

export function SidebarContextProvider({ children }: { children: ReactNode }) {
  const [sidebarVisible, setSidebarVisible] = useState(true)
  const [isMobile, setIsMobile] = useState(false)

  useEffect(() => {
    const checkScreenSize = () => {
      const mobile = window.innerWidth < 768 // md breakpoint
      const wasMobile = isMobile
      setIsMobile(mobile)
      
      // When switching to mobile, hide sidebar
      if (mobile && !wasMobile) {
        setSidebarVisible(false)
      }
      // When switching to desktop, show sidebar
      else if (!mobile && wasMobile) {
        setSidebarVisible(true)
      }
    }

    checkScreenSize()
    window.addEventListener('resize', checkScreenSize)
    return () => window.removeEventListener('resize', checkScreenSize)
  }, [isMobile])

  return (
    <SidebarContext.Provider value={{ sidebarVisible, setSidebarVisible, isMobile }}>
      {children}
    </SidebarContext.Provider>
  )
}

export function useSidebarContext() {
  const context = useContext(SidebarContext)
  if (context === undefined) {
    throw new Error("useSidebarContext must be used within a SidebarContextProvider")
  }
  return context
}
