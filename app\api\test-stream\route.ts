import { NextRequest } from 'next/server'

export async function GET(_request: NextRequest) {
  const encoder = new TextEncoder()
  
  const stream = new ReadableStream({
    async start(controller) {
      try {
        // Send test tokens one by one
        const testMessage = "Hello world! This is a streaming test. Each word should appear one by one."
        const words = testMessage.split(' ')
        
        for (let i = 0; i < words.length; i++) {
          const word = words[i] + (i < words.length - 1 ? ' ' : '')
          
          controller.enqueue(
            encoder.encode(`data: ${JSON.stringify({
              type: 'token',
              content: word
            })}\n\n`)
          )
          
          console.log(`Sent word ${i + 1}: "${word}"`)
          
          // Wait 200ms between words to make streaming visible
          await new Promise(resolve => setTimeout(resolve, 200))
        }
        
        // Send completion
        controller.enqueue(
          encoder.encode(`data: ${JSON.stringify({
            type: 'complete',
            content: 'Test completed'
          })}\n\n`)
        )
        
      } catch (error) {
        console.error('Test stream error:', error)
      } finally {
        controller.close()
      }
    }
  })

  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
    }
  })
}
