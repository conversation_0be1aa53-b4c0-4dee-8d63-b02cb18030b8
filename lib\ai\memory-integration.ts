import { memoryService } from './memory-service'
import { ConversationContext } from './providers'
import { contextEngineer, ContextStrategy } from './context-engineering'
import { contextStrategySelector } from './context-strategy-selector'

/**
 * Memory integration utilities for AI providers
 * Handles conversation storage and educational context building
 */

/**
 * Build educational context using intelligent strategy selection and advanced context engineering
 * Automatically selects optimal context strategy based on user behavior and learning patterns
 * 
 * @param context - Current conversation context
 * @param strategy - Optional override for context selection strategy
 * @returns Optimized educational context string
 */
export async function buildEducationalContext(
  context: ConversationContext,
  strategy?: ContextStrategy
): Promise<string> {
  try {
    // Auto-select strategy if not provided
    const selectedStrategy = strategy || await contextStrategySelector.selectOptimalStrategy(
      context.currentQuery,
      context.bloomLevel,
      context.documentId, // Using documentId as sessionId
      context.chunkIndex
    )

    console.log(`🧠 Building educational context with strategy: ${selectedStrategy} for query: "${context.currentQuery.substring(0, 50)}..."`)

    // Try advanced context engineering first
    try {
      return await contextEngineer.buildIntelligentContext(
        context.documentId, // Using documentId as sessionId
        context.currentQuery,
        context.chunkContent,
        selectedStrategy,
        context.bloomLevel
      )
    } catch (engineeringError) {
      console.warn('Advanced context engineering failed, falling back to basic memory service:', engineeringError)

      // Fallback to basic memory service
      try {
        return await memoryService.buildEducationalContext(
          context.documentId,
          context.userId,
          context.currentQuery,
          context.chunkContent
        )
      } catch (memoryError) {
        console.error('Memory service failed, using minimal context:', memoryError)

        // Final fallback to minimal context
        return `CURRENT CONTENT:\n${context.chunkContent}\n\nSTUDENT QUESTION: ${context.currentQuery}\n`
      }
    }
  } catch (error) {
    console.error('Context building failed completely, using minimal context:', error)

    // Absolute fallback to minimal context
    return `CURRENT CONTENT:\n${context.chunkContent}\n\nSTUDENT QUESTION: ${context.currentQuery}\n`
  }
}

/**
 * Store conversation messages in memory after AI response is complete
 * Saves both user message and AI response for future context building
 * 
 * @param context - Current conversation context
 * @param userMessage - The user's message/question
 * @param aiResponse - The AI's complete response
 */
export async function storeConversationMemory(
  context: ConversationContext,
  userMessage: string,
  aiResponse: string
): Promise<void> {
  try {
    // Store user message in memory (with graceful failure)
    try {
      await memoryService.addConversationMessage(
        context.documentId,
        context.userId,
        userMessage,
        'user',
        context.chunkIndex
      )
    } catch (error) {
      console.warn('Failed to store user message in memory:', error)
    }

    // Store AI response in memory (with graceful failure)
    try {
      await memoryService.addConversationMessage(
        context.documentId,
        context.userId,
        aiResponse,
        'assistant',
        context.chunkIndex
      )
    } catch (error) {
      console.warn('Failed to store AI response in memory:', error)
    }

    // Add learning progress tracking if Bloom level is specified (with graceful failure)
    if (context.bloomLevel) {
      try {
        await memoryService.addLearningMemory(
          context.documentId,
          context.userId,
          `Student engaged with ${context.bloomLevel} level content about chunk ${context.chunkIndex}`,
          'learning_progress',
          context.bloomLevel
        )
      } catch (error) {
        console.warn('Failed to store learning progress:', error)
      }
    }
  } catch (error) {
    console.error('Failed to store conversation memory:', error)
    // Don't throw error - memory is enhancement, not critical for core functionality
  }
}

/**
 * Analyze AI response for potential misconceptions or learning insights
 * This is a simplified version - could be enhanced with NLP analysis
 * 
 * @param userMessage - The user's message
 * @param aiResponse - The AI's response
 * @param context - Current conversation context
 */
export async function analyzeResponseForInsights(
  userMessage: string,
  aiResponse: string,
  context: ConversationContext
): Promise<void> {
  try {
    // Simple keyword-based misconception detection
    const misconceptionKeywords = ['incorrect', 'wrong', 'mistake', 'misunderstanding', 'actually']
    const hasMisconception = misconceptionKeywords.some(keyword => 
      aiResponse.toLowerCase().includes(keyword)
    )

    if (hasMisconception) {
      await memoryService.addMisconception(
        context.documentId,
        context.userId,
        `Potential misconception in user message: ${userMessage}`,
        `AI correction provided: ${aiResponse.substring(0, 200)}...`
      )
    }

    // Track concept understanding based on response patterns
    const understandingKeywords = ['explain', 'understand', 'clear', 'makes sense']
    const showsUnderstanding = understandingKeywords.some(keyword =>
      userMessage.toLowerCase().includes(keyword)
    )

    if (showsUnderstanding) {
      await memoryService.addConceptUnderstanding(
        context.documentId,
        context.userId,
        `Chunk ${context.chunkIndex} content`,
        4, // Good understanding level
        `User demonstrated understanding: ${userMessage.substring(0, 100)}...`
      )
    }
  } catch (error) {
    console.error('Failed to analyze response for insights:', error)
  }
}

/**
 * Initialize memory session for new document
 * Sets up the memory context for a new learning session
 * 
 * @param userId - User identifier
 * @param documentId - Document identifier
 * @returns Session ID for memory operations
 */
export async function initializeMemorySession(userId: string, documentId: string): Promise<string> {
  try {
    return await memoryService.initializeSession(userId, documentId)
  } catch (error) {
    console.error('Failed to initialize memory session:', error)
    // Return fallback session ID
    return `${userId}-${documentId}-${Date.now()}`
  }
}

/**
 * Clean up memory session when document interaction ends
 *
 * @param documentId - Document identifier to clean up
 * @param userId - User identifier
 */
export async function cleanupMemorySession(documentId: string, userId: string): Promise<void> {
  try {
    await memoryService.deleteDocumentMemories(documentId, userId)
  } catch (error) {
    console.error('Failed to cleanup memory session:', error)
  }
}
