/**
 * Real Integration Test
 * This tests actual API connections (not mocks)
 */

// Test OpenAI connection
async function testOpenAI() {
  console.log('🔍 Testing OpenAI connection...')
  
  try {
    const { OpenAIProvider } = require('./lib/ai/providers')
    const provider = new OpenAIProvider()
    await provider.initialize()
    
    console.log('✅ OpenAI provider initialized successfully')
    console.log('🔑 API Key configured:', process.env.OPENAI_API_KEY ? 'Yes' : 'No')
    
    return true
  } catch (error) {
    console.log('❌ OpenAI test failed:', error.message)
    return false
  }
}

// Test Anthropic connection  
async function testAnthropic() {
  console.log('🔍 Testing Anthropic connection...')
  
  try {
    const { AnthropicProvider } = require('./lib/ai/providers')
    const provider = new AnthropicProvider()
    await provider.initialize()
    
    console.log('✅ Anthropic provider initialized successfully')
    console.log('🔑 API Key configured:', process.env.ANTHROPIC_API_KEY ? 'Yes' : 'No')
    
    return true
  } catch (error) {
    console.log('❌ Anthropic test failed:', error.message)
    return false
  }
}

// Test Mem0 connection
async function testMem0() {
  console.log('🔍 Testing Mem0 connection...')
  
  try {
    const { MemoryService } = require('./lib/ai/memory-service')
    const memoryService = new MemoryService()
    await memoryService.initialize()
    
    console.log('✅ Mem0 service initialized successfully')
    console.log('🔑 API Key configured:', process.env.MEM0_API_KEY ? 'Yes' : 'No')
    
    return true
  } catch (error) {
    console.log('❌ Mem0 test failed:', error.message)
    return false
  }
}

// Run all tests
async function runRealTests() {
  console.log('🚀 Starting Real Integration Tests...\n')
  
  const results = {
    openai: await testOpenAI(),
    anthropic: await testAnthropic(), 
    mem0: await testMem0()
  }
  
  console.log('\n📊 Results Summary:')
  console.log('OpenAI:', results.openai ? '✅ PASS' : '❌ FAIL')
  console.log('Anthropic:', results.anthropic ? '✅ PASS' : '❌ FAIL')
  console.log('Mem0:', results.mem0 ? '✅ PASS' : '❌ FAIL')
  
  const allPassed = Object.values(results).every(result => result)
  console.log('\n🎯 Overall:', allPassed ? '✅ READY FOR REAL USE' : '❌ NEEDS FIXING')
}

runRealTests().catch(console.error)