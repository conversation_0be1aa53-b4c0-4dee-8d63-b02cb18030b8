"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"

export default function TestStreamingPage() {
  const [messages, setMessages] = useState<string>("")
  const [isStreaming, setIsStreaming] = useState(false)

  const testStreaming = async () => {
    setIsStreaming(true)
    setMessages("")

    try {
      const response = await fetch("/api/test-stream")
      
      if (!response.ok) {
        throw new Error("Failed to start stream")
      }

      const reader = response.body?.getReader()
      if (!reader) throw new Error("No response body")

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = new TextDecoder().decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))
              if (data.type === 'token') {
                setMessages(prev => prev + data.content)
              } else if (data.type === 'complete') {
                console.log('Stream completed:', data.content)
              }
            } catch (e) {
              console.warn('Parse error:', e)
            }
          }
        }
      }
    } catch (error) {
      console.error('Streaming test failed:', error)
    } finally {
      setIsStreaming(false)
    }
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">Streaming Test</h1>
      
      <Button 
        onClick={testStreaming} 
        disabled={isStreaming}
        className="mb-4"
      >
        {isStreaming ? 'Streaming...' : 'Test Streaming'}
      </Button>
      
      <div className="border p-4 min-h-[200px] bg-gray-50">
        <h2 className="font-semibold mb-2">Streamed Content:</h2>
        <div className="whitespace-pre-wrap">{messages}</div>
      </div>
    </div>
  )
}
