import { NextRequest, NextResponse } from 'next/server'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Memory Service Test API Endpoint
 * Tests Mem0 connection and basic operations
 */

/**
 * GET /api/memory/test
 * Test memory service connection and basic operations
 */
export async function GET() {
  try {
    // Test connection
    const isConnected = await memoryService.testConnection()
    
    if (!isConnected) {
      return NextResponse.json(
        { 
          success: false, 
          error: 'Failed to connect to Mem0 service' 
        },
        { status: 500 }
      )
    }

    // Test document isolation
    const timestamp = Date.now()
    const testUserId = `test-user-${timestamp}`
    const testDocumentId1 = `test-doc-1-${timestamp}`
    const testDocumentId2 = `test-doc-2-${timestamp}`

    try {
      // Test adding memories to two different documents
      await memoryService.addConversationMessage(
        testDocumentId1,
        testUserId,
        'Message for document 1',
        'user'
      )

      await memoryService.addConversationMessage(
        testDocumentId2,
        testUserId,
        'Message for document 2',
        'user'
      )

      // Test retrieving memories for each document separately
      const memories1 = await memoryService.getAllMemories(testDocumentId1, testUserId)
      const memories2 = await memoryService.getAllMemories(testDocumentId2, testUserId)

      // Verify isolation - each document should only have its own memories
      const doc1HasDoc2Content = memories1.some(m =>
        (m.memory || m.content || '').includes('document 2')
      )
      const doc2HasDoc1Content = memories2.some(m =>
        (m.memory || m.content || '').includes('document 1')
      )

      // Clean up test data
      await memoryService.deleteDocumentMemories(testDocumentId1, testUserId)
      await memoryService.deleteDocumentMemories(testDocumentId2, testUserId)

      return NextResponse.json({
        success: true,
        message: 'Memory service is working correctly with document isolation',
        testResults: {
          connection: true,
          addMessage: true,
          retrieveMemories: true,
          document1MemoriesCount: memories1.length,
          document2MemoriesCount: memories2.length,
          isolationWorking: !doc1HasDoc2Content && !doc2HasDoc1Content,
          cleanup: true
        }
      })
    } catch (operationError) {
      // Clean up on error
      try {
        await memoryService.deleteDocumentMemories(testDocumentId1, testUserId)
        await memoryService.deleteDocumentMemories(testDocumentId2, testUserId)
      } catch (cleanupError) {
        console.error('Failed to cleanup test sessions:', cleanupError)
      }

      throw operationError
    }
  } catch (error) {
    console.error('Memory service test failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Memory service test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/memory/test
 * Test memory service with custom session
 */
export async function POST(request: NextRequest) {
  try {
    const { documentId, userId, message, role = 'user' } = await request.json()

    if (!documentId || !userId || !message) {
      return NextResponse.json(
        { error: 'documentId, userId, and message are required' },
        { status: 400 }
      )
    }

    // Add test message
    await memoryService.addConversationMessage(documentId, userId, message, role as 'user' | 'assistant')

    // Retrieve all memories for this document
    const memories = await memoryService.getAllMemories(documentId, userId)

    return NextResponse.json({
      success: true,
      message: 'Message added successfully',
      documentId,
      userId,
      memoriesCount: memories.length,
      latestMemories: memories.slice(-5) // Return last 5 memories
    })
  } catch (error) {
    console.error('Memory service POST test failed:', error)
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to add message to memory',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
