import { memoryService, BloomLevel } from './memory-service'

/**
 * Advanced context engineering service using Mem0 v2 features
 * Implements intelligent context selection and criteria-based retrieval
 * for optimal educational outcomes
 */

/**
 * Educational relevance criteria for memory retrieval
 * Used to score and rank memories based on learning objectives
 */
export interface EducationalCriteria {
  name: string
  description: string
  weight: number
}

/**
 * Context selection strategy for different learning scenarios
 * Determines how memories are filtered and prioritized
 */
export enum ContextStrategy {
  MISCONCEPTION_FOCUSED = 'misconception_focused',
  CONCEPT_BUILDING = 'concept_building',
  APPLICATION_ORIENTED = 'application_oriented',
  REVIEW_MODE = 'review_mode',
  DISCOVERY_MODE = 'discovery_mode'
}

/**
 * Token management configuration for context window optimization
 */
interface TokenConfig {
  maxContextTokens: number
  chunkContentTokens: number
  systemPromptTokens: number
  availableForMemory: number
}

/**
 * Advanced context engineering class
 * Implements sophisticated memory retrieval and context building strategies
 */
export class ContextEngineer {
  private readonly DEFAULT_CRITERIA: EducationalCriteria[] = [
    {
      name: 'educational_relevance',
      description: 'How relevant is this memory to current learning objective',
      weight: 0.4
    },
    {
      name: 'understanding_level',
      description: 'Match with student current understanding level',
      weight: 0.3
    },
    {
      name: 'recency',
      description: 'How recent is this learning interaction',
      weight: 0.2
    },
    {
      name: 'misconception_risk',
      description: 'Risk of reinforcing misconceptions',
      weight: 0.1
    }
  ]

  /**
   * Build intelligent educational context using advanced retrieval strategies
   * 
   * @param sessionId - Memory session identifier
   * @param currentQuery - User's current question
   * @param chunkContent - Current document chunk content
   * @param strategy - Context selection strategy
   * @param bloomLevel - Current Bloom's taxonomy level
   * @returns Optimized educational context string
   */
  async buildIntelligentContext(
    sessionId: string,
    currentQuery: string,
    chunkContent: string,
    strategy: ContextStrategy = ContextStrategy.CONCEPT_BUILDING,
    bloomLevel?: BloomLevel
  ): Promise<string> {
    // Calculate token budget for context
    const tokenConfig = this.calculateTokenBudget(chunkContent, currentQuery)
    
    // Get strategy-specific criteria
    const criteria = this.getCriteriaForStrategy(strategy)
    
    // Retrieve relevant memories using criteria-based search
    const relevantMemories = await this.retrieveRelevantMemories(
      sessionId,
      currentQuery,
      criteria,
      bloomLevel
    )
    
    // Get misconceptions that need addressing
    const misconceptions = await this.getMisconceptionsToAddress(sessionId)
    
    // Build optimized context within token limits
    return this.buildOptimizedContext(
      chunkContent,
      currentQuery,
      relevantMemories,
      misconceptions,
      tokenConfig,
      strategy
    )
  }

  /**
   * Retrieve relevant memories using criteria-based scoring
   * Implements Mem0 v2 advanced retrieval features
   * 
   * @param sessionId - Memory session identifier
   * @param query - Current user query
   * @param criteria - Educational relevance criteria
   * @param bloomLevel - Optional Bloom's taxonomy level filter
   * @returns Scored and ranked relevant memories
   */
  private async retrieveRelevantMemories(
    sessionId: string,
    query: string,
    criteria: EducationalCriteria[],
    bloomLevel?: BloomLevel
  ): Promise<any[]> {
    try {
      // Use Mem0's criteria-based retrieval (v2 feature)
      const searchParams: any = {
        query,
        user_id: sessionId,
        criteria: criteria.map(c => ({
          name: c.name,
          description: c.description,
          weight: c.weight
        })),
        advanced_retrieval: {
          keyword_search: true,
          ranking_function: 'educational_priority'
        },
        limit: 8 // Get more memories for better selection
      }

      // Add Bloom level filter if specified
      if (bloomLevel) {
        searchParams.filters = {
          bloom_level: bloomLevel
        }
      }

      return await memoryService.getContextualMemory(sessionId, query, bloomLevel) || []
    } catch (error) {
      console.error('Failed to retrieve relevant memories:', error)
      return []
    }
  }

  /**
   * Get misconceptions that need to be addressed in current context
   * Prioritizes recent and reinforcement-needed misconceptions
   * 
   * @param sessionId - Memory session identifier
   * @returns Array of misconceptions to address
   */
  private async getMisconceptionsToAddress(sessionId: string): Promise<any[]> {
    try {
      return await memoryService.getMisconceptions(sessionId) || []
    } catch (error) {
      console.error('Failed to get misconceptions:', error)
      return []
    }
  }

  /**
   * Get educational criteria based on learning strategy
   * Different strategies prioritize different aspects of memory retrieval
   * 
   * @param strategy - Context selection strategy
   * @returns Customized criteria for the strategy
   */
  private getCriteriaForStrategy(strategy: ContextStrategy): EducationalCriteria[] {
    switch (strategy) {
      case ContextStrategy.MISCONCEPTION_FOCUSED:
        return [
          { name: 'misconception_risk', description: 'Identify and correct misconceptions', weight: 0.5 },
          { name: 'educational_relevance', description: 'Relevance to current topic', weight: 0.3 },
          { name: 'recency', description: 'Recent misconceptions need priority', weight: 0.2 }
        ]

      case ContextStrategy.APPLICATION_ORIENTED:
        return [
          { name: 'application_potential', description: 'Can be applied to real scenarios', weight: 0.4 },
          { name: 'understanding_level', description: 'Match application complexity', weight: 0.3 },
          { name: 'educational_relevance', description: 'Relevance to current learning', weight: 0.3 }
        ]

      case ContextStrategy.REVIEW_MODE:
        return [
          { name: 'concept_coverage', description: 'Covers key concepts learned', weight: 0.4 },
          { name: 'understanding_gaps', description: 'Areas needing reinforcement', weight: 0.3 },
          { name: 'recency', description: 'Recent learning for retention', weight: 0.3 }
        ]

      case ContextStrategy.DISCOVERY_MODE:
        return [
          { name: 'curiosity_potential', description: 'Can spark curiosity and questions', weight: 0.4 },
          { name: 'connection_building', description: 'Connects to existing knowledge', weight: 0.3 },
          { name: 'educational_relevance', description: 'Relevant to discovery path', weight: 0.3 }
        ]

      default: // CONCEPT_BUILDING
        return this.DEFAULT_CRITERIA
    }
  }

  /**
   * Calculate optimal token budget for context components
   * Ensures context fits within model limits while maximizing educational value
   * 
   * @param chunkContent - Current chunk content
   * @param currentQuery - User's query
   * @returns Token configuration for context building
   */
  private calculateTokenBudget(chunkContent: string, currentQuery: string): TokenConfig {
    // Rough token estimation (1 token ≈ 4 characters)
    const estimateTokens = (text: string) => Math.ceil(text.length / 4)
    
    const maxContextTokens = 3000 // Conservative limit for most models
    const chunkContentTokens = estimateTokens(chunkContent)
    const queryTokens = estimateTokens(currentQuery)
    const systemPromptTokens = 800 // Estimated system prompt size
    
    const availableForMemory = Math.max(
      0,
      maxContextTokens - chunkContentTokens - queryTokens - systemPromptTokens
    )

    return {
      maxContextTokens,
      chunkContentTokens,
      systemPromptTokens,
      availableForMemory
    }
  }

  /**
   * Build optimized context string within token limits
   * Prioritizes most important information based on strategy
   * 
   * @param chunkContent - Current chunk content
   * @param currentQuery - User's query
   * @param memories - Retrieved relevant memories
   * @param misconceptions - Misconceptions to address
   * @param tokenConfig - Token budget configuration
   * @param strategy - Context selection strategy
   * @returns Optimized context string
   */
  private buildOptimizedContext(
    chunkContent: string,
    currentQuery: string,
    memories: any[],
    misconceptions: any[],
    tokenConfig: TokenConfig,
    strategy: ContextStrategy
  ): string {
    let context = `CURRENT CONTENT:\n${chunkContent}\n\n`
    let usedTokens = tokenConfig.chunkContentTokens

    // Add misconceptions first if strategy prioritizes them
    if (strategy === ContextStrategy.MISCONCEPTION_FOCUSED && misconceptions.length > 0) {
      const misconceptionText = this.buildMisconceptionContext(misconceptions, tokenConfig.availableForMemory / 2)
      if (misconceptionText) {
        context += misconceptionText
        usedTokens += Math.ceil(misconceptionText.length / 4)
      }
    }

    // Add relevant memories within remaining token budget
    const remainingTokens = tokenConfig.availableForMemory - (usedTokens - tokenConfig.chunkContentTokens)
    if (remainingTokens > 0 && memories.length > 0) {
      const memoryContext = this.buildMemoryContext(memories, remainingTokens, strategy)
      if (memoryContext) {
        context += memoryContext
      }
    }

    // Add misconceptions at the end if not already added
    if (strategy !== ContextStrategy.MISCONCEPTION_FOCUSED && misconceptions.length > 0) {
      const remainingSpace = tokenConfig.availableForMemory - (context.length / 4 - tokenConfig.chunkContentTokens)
      if (remainingSpace > 100) { // Only if we have reasonable space
        const misconceptionText = this.buildMisconceptionContext(misconceptions, remainingSpace)
        if (misconceptionText) {
          context += misconceptionText
        }
      }
    }

    context += `STUDENT QUESTION: ${currentQuery}\n`
    return context
  }

  /**
   * Build misconception context section
   * 
   * @param misconceptions - Array of misconceptions
   * @param tokenLimit - Token limit for this section
   * @returns Formatted misconception context
   */
  private buildMisconceptionContext(misconceptions: any[], tokenLimit: number): string {
    if (misconceptions.length === 0) return ''

    let context = 'MISCONCEPTIONS TO ADDRESS:\n'
    let usedTokens = Math.ceil(context.length / 4)

    for (const misc of misconceptions.slice(0, 3)) { // Limit to top 3
      const miscText = `- ${misc.memory}\n`
      const miscTokens = Math.ceil(miscText.length / 4)
      
      if (usedTokens + miscTokens > tokenLimit) break
      
      context += miscText
      usedTokens += miscTokens
    }

    return context + '\n'
  }

  /**
   * Build memory context section with strategy-based prioritization
   * 
   * @param memories - Array of relevant memories
   * @param tokenLimit - Token limit for this section
   * @param strategy - Context selection strategy
   * @returns Formatted memory context
   */
  private buildMemoryContext(memories: any[], tokenLimit: number, strategy: ContextStrategy): string {
    if (memories.length === 0) return ''

    const sectionTitle = this.getMemoryContextTitle(strategy)
    let context = `${sectionTitle}:\n`
    let usedTokens = Math.ceil(context.length / 4)

    // Sort memories by relevance score if available
    const sortedMemories = memories.sort((a, b) => (b.score || 0) - (a.score || 0))

    for (let i = 0; i < Math.min(sortedMemories.length, 5); i++) {
      const memory = sortedMemories[i]
      const memoryText = `${i + 1}. ${memory.memory}\n`
      const memoryTokens = Math.ceil(memoryText.length / 4)
      
      if (usedTokens + memoryTokens > tokenLimit) break
      
      context += memoryText
      usedTokens += memoryTokens
    }

    return context + '\n'
  }

  /**
   * Get appropriate section title based on strategy
   * 
   * @param strategy - Context selection strategy
   * @returns Section title for memory context
   */
  private getMemoryContextTitle(strategy: ContextStrategy): string {
    switch (strategy) {
      case ContextStrategy.MISCONCEPTION_FOCUSED:
        return 'RELATED LEARNING CORRECTIONS'
      case ContextStrategy.APPLICATION_ORIENTED:
        return 'APPLICABLE KNOWLEDGE'
      case ContextStrategy.REVIEW_MODE:
        return 'PREVIOUS LEARNING SUMMARY'
      case ContextStrategy.DISCOVERY_MODE:
        return 'CONNECTED CONCEPTS'
      default:
        return 'RELEVANT LEARNING CONTEXT'
    }
  }
}

// Export singleton instance
export const contextEngineer = new ContextEngineer()
