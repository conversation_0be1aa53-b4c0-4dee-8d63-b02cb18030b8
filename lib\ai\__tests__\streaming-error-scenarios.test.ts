/**
 * Streaming and Error Scenarios Tests
 * Tests for streaming functionality and various error conditions
 */

// Jest globals are available globally, no need to import
import { StreamBuffer, AdaptiveStreamBuffer } from '../stream-buffer'
import { fallbackManager } from '../fallback-manager'
import { <PERSON><PERSON>rrorHandler, ErrorType } from '../error-handler'

describe('Streaming Functionality', () => {
  describe('StreamBuffer', () => {
    let buffer: StreamBuffer
    let flushedTokens: any[]

    beforeEach(() => {
      flushedTokens = []
      buffer = new StreamBuffer((tokens) => {
        flushedTokens.push(...tokens)
      }, {
        bufferSize: 3,
        flushInterval: 50,
        minFlushSize: 1,
        maxWaitTime: 100,
        enableSmoothing: true
      })
    })

    it('should buffer tokens and flush when buffer is full', async () => {
      buffer.start()
      
      buffer.addToken('Hello')
      buffer.addToken(' ')
      buffer.addToken('world')
      
      // Should auto-flush when buffer is full
      await new Promise(resolve => setTimeout(resolve, 10))
      
      expect(flushedTokens).toHaveLength(3)
      expect(flushedTokens.map(t => t.content)).toEqual(['Hello', ' ', 'world'])
    })

    it('should flush tokens after interval', async () => {
      buffer.start()
      
      buffer.addToken('Hello')
      buffer.addToken(' ')
      
      // Wait for flush interval
      await new Promise(resolve => setTimeout(resolve, 60))
      
      expect(flushedTokens).toHaveLength(2)
    })

    it('should flush tokens when max wait time exceeded', async () => {
      buffer.start()
      
      buffer.addToken('Hello')
      
      // Wait for max wait time
      await new Promise(resolve => setTimeout(resolve, 110))
      
      expect(flushedTokens).toHaveLength(1)
    })

    it('should flush immediately when smoothing disabled', () => {
      const immediateBuffer = new StreamBuffer((tokens) => {
        flushedTokens.push(...tokens)
      }, { enableSmoothing: false })

      immediateBuffer.addToken('Hello')
      
      expect(flushedTokens).toHaveLength(1)
      expect(flushedTokens[0].content).toBe('Hello')
    })

    it('should force flush all tokens', async () => {
      buffer.start()
      
      buffer.addToken('Hello')
      buffer.addToken(' ')
      
      await buffer.forceFlush()
      
      expect(flushedTokens).toHaveLength(2)
    })

    it('should provide accurate statistics', () => {
      buffer.start()
      buffer.addToken('Hello')
      buffer.addToken(' ')
      
      const stats = buffer.getStats()
      
      expect(stats.bufferedTokens).toBe(2)
      expect(stats.totalProcessed).toBe(2)
      expect(stats.isActive).toBe(true)
    })
  })

  describe('AdaptiveStreamBuffer', () => {
    let adaptiveBuffer: AdaptiveStreamBuffer
    let flushedTokens: any[]

    beforeEach(() => {
      flushedTokens = []
      adaptiveBuffer = new AdaptiveStreamBuffer((tokens) => {
        flushedTokens.push(...tokens)
      })
    })

    it('should adapt buffer settings based on latency', async () => {
      adaptiveBuffer.start()
      
      // Simulate high latency scenario
      for (let i = 0; i < 10; i++) {
        adaptiveBuffer.addToken(`token${i}`)
        await new Promise(resolve => setTimeout(resolve, 20))
      }
      
      const stats = adaptiveBuffer.getAdaptationStats()
      expect(stats.adaptationEnabled).toBe(true)
      expect(stats.latencyHistory.length).toBeGreaterThan(0)
    })

    it('should allow disabling adaptation', () => {
      adaptiveBuffer.setAdaptationEnabled(false)
      
      const stats = adaptiveBuffer.getAdaptationStats()
      expect(stats.adaptationEnabled).toBe(false)
    })
  })
})

describe('Error Handling Scenarios', () => {
  describe('AIErrorHandler', () => {
    it('should classify OpenAI authentication errors', () => {
      const error = { status: 401, message: 'Invalid API key' }
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.AUTHENTICATION_ERROR)
      expect(classified.retryable).toBe(false)
      expect(classified.fallbackAvailable).toBe(true)
    })

    it('should classify rate limit errors', () => {
      const error = { status: 429, message: 'Rate limit exceeded' }
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.API_RATE_LIMIT)
      expect(classified.retryable).toBe(true)
      expect(classified.fallbackAvailable).toBe(true)
    })

    it('should classify network errors', () => {
      const error = new Error('ECONNREFUSED')
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.NETWORK_ERROR)
      expect(classified.retryable).toBe(true)
    })

    it('should classify timeout errors', () => {
      const error = new Error('Request timeout')
      const classified = AIErrorHandler.classifyError(error, 'anthropic')
      
      expect(classified.type).toBe(ErrorType.TIMEOUT_ERROR)
      expect(classified.retryable).toBe(true)
    })

    it('should classify unknown errors', () => {
      const error = new Error('Something weird happened')
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.UNKNOWN_ERROR)
      expect(classified.retryable).toBe(true)
    })

    it('should provide user-friendly error messages', () => {
      const authError = {
        type: ErrorType.AUTHENTICATION_ERROR,
        message: 'Auth failed',
        retryable: false,
        fallbackAvailable: true,
        provider: 'openai'
      }
      
      const userMessage = AIErrorHandler.getUserMessage(authError)
      expect(userMessage).toContain('Authentication failed')
    })

    it('should determine when to fallback', () => {
      const authError = {
        type: ErrorType.AUTHENTICATION_ERROR,
        message: 'Auth failed',
        retryable: false,
        fallbackAvailable: true,
        provider: 'openai'
      }
      
      expect(AIErrorHandler.shouldFallback(authError)).toBe(true)
      
      const networkError = {
        type: ErrorType.NETWORK_ERROR,
        message: 'Network failed',
        retryable: true,
        fallbackAvailable: false,
        provider: 'openai'
      }
      
      expect(AIErrorHandler.shouldFallback(networkError)).toBe(false)
    })

    it('should retry operations with exponential backoff', async () => {
      let attempts = 0
      const operation = jest.fn().mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          throw new Error('Temporary failure')
        }
        return 'success'
      })

      const result = await AIErrorHandler.withRetry(operation, 'test', 3)
      
      expect(result).toBe('success')
      expect(attempts).toBe(3)
    })

    it('should stop retrying non-retryable errors', async () => {
      const operation = jest.fn().mockImplementation(() => {
        const error = new Error('Authentication failed')
        throw error
      })

      try {
        await AIErrorHandler.withRetry(operation, 'openai', 3)
        fail('Expected function to throw')
      } catch (error) {
        expect(error).toBeDefined()
        expect(operation).toHaveBeenCalledTimes(1) // Should not retry auth errors
      }
    })
  })

  describe('Fallback Manager Error Scenarios', () => {
    beforeEach(() => {
      // Reset environment
      process.env.OPENAI_API_KEY = 'test-key'
      process.env.ANTHROPIC_API_KEY = 'test-key'
      jest.clearAllMocks()
    })

    it('should switch providers on failure', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValueOnce(new Error('OpenAI failed'))
        .mockResolvedValueOnce('Anthropic success')

      // Mock the fallback manager to simulate provider switching
      const testFallbackManager = {
        ...fallbackManager,
        executeWithFallback: jest.fn().mockImplementation(async (operation: any) => {
          try {
            return await operation({ name: 'openai' })
          } catch (error) {
            // Simulate fallback to Anthropic
            return await operation({ name: 'anthropic' })
          }
        })
      }

      const result = await testFallbackManager.executeWithFallback(mockOperation)
      expect(result).toBe('Anthropic success')
    })

    it('should handle all providers failing', async () => {
      const mockOperation = jest.fn()
        .mockRejectedValue(new Error('All providers failed'))

      const testFallbackManager = {
        ...fallbackManager,
        executeWithFallback: jest.fn().mockImplementation(async (operation: any) => {
          // Try all providers, all fail
          await expect(operation({ name: 'openai' })).rejects.toThrow()
          await expect(operation({ name: 'anthropic' })).rejects.toThrow()
          throw new Error('All fallback attempts failed')
        })
      }

      await expect(testFallbackManager.executeWithFallback(mockOperation))
        .rejects.toThrow('All fallback attempts failed')
    })
  })

  describe('Memory Service Error Scenarios', () => {
    it('should handle Mem0 API failures gracefully', async () => {
      const { MemoryService } = require('../memory-service')
      
      // Mock Mem0 client to fail
      const mockClient = {
        add: jest.fn().mockRejectedValue(new Error('Mem0 API failed')),
        search: jest.fn().mockRejectedValue(new Error('Mem0 API failed')),
        getAll: jest.fn().mockRejectedValue(new Error('Mem0 API failed'))
      }

      const memoryService = new MemoryService()
      ;(memoryService as any).client = mockClient
      ;(memoryService as any).available = false

      // These should not throw errors
      await expect(memoryService.addConversationMessage('session', 'message', 'user'))
        .resolves.not.toThrow()
      
      const memories = await memoryService.getContextualMemory('session', 'query')
      expect(memories).toEqual([])
    })

    it('should handle database connection failures', async () => {
      const { MemoryService } = require('../memory-service')
      
      // Mock database service to fail
      jest.doMock('@/lib/db-service', () => ({
        dbService: {
          progress: {
            upsert: jest.fn().mockRejectedValue(new Error('Database connection failed'))
          }
        }
      }))

      const memoryService = new MemoryService()
      
      await expect(memoryService.initializeSession('user', 'doc'))
        .rejects.toThrow()
    })
  })

  describe('Streaming Error Recovery', () => {
    it('should handle streaming interruptions', async () => {
      const mockProvider = {
        generateResponse: jest.fn().mockImplementation(async function* () {
          yield 'Hello'
          yield ' '
          throw new Error('Stream interrupted')
        })
      }

      const tokens: string[] = []
      
      try {
        for await (const token of mockProvider.generateResponse('test', {})) {
          tokens.push(token)
        }
      } catch (error) {
        // Should have partial tokens before error
        expect(tokens).toEqual(['Hello', ' '])
        expect((error as Error).message).toBe('Stream interrupted')
      }
    })

    it('should handle malformed streaming responses', async () => {
      const mockProvider = {
        generateResponse: jest.fn().mockImplementation(async function* () {
          yield 'Valid token'
          yield null // Malformed token
          yield 'Another valid token'
        })
      }

      const tokens: string[] = []
      
      for await (const token of mockProvider.generateResponse('test', {})) {
        if (token) { // Filter out null tokens
          tokens.push(token)
        }
      }
      
      expect(tokens).toEqual(['Valid token', 'Another valid token'])
    })
  })
})