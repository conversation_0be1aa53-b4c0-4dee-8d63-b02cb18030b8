/**
 * Session State Management Utility
 * Prevents unnecessary session reinitializations and tracks session state
 */

interface SessionState {
  documentId: string
  sessionId: string | null
  initialized: boolean
  hasMessages: boolean
  currentChunk: number
  totalChunks: number
  lastAccessed: number
}

class SessionStateManager {
  private sessions: Map<string, SessionState> = new Map()
  private readonly SESSION_TIMEOUT = 30 * 60 * 1000 // 30 minutes

  /**
   * Check if a session is already initialized and valid
   */
  isSessionInitialized(documentId: string): boolean {
    const session = this.sessions.get(documentId)
    if (!session) return false

    // Check if session has expired
    const now = Date.now()
    if (now - session.lastAccessed > this.SESSION_TIMEOUT) {
      this.sessions.delete(documentId)
      return false
    }

    return session.initialized && session.sessionId !== null
  }

  /**
   * Get session state for a document
   */
  getSessionState(documentId: string): SessionState | null {
    const session = this.sessions.get(documentId)
    if (!session) return null

    // Check if session has expired
    const now = Date.now()
    if (now - session.lastAccessed > this.SESSION_TIMEOUT) {
      this.sessions.delete(documentId)
      return null
    }

    // Update last accessed time
    session.lastAccessed = now
    return session
  }

  /**
   * Update session state
   */
  updateSessionState(documentId: string, updates: Partial<SessionState>): void {
    const existing = this.sessions.get(documentId)
    const now = Date.now()

    const sessionState: SessionState = {
      documentId,
      sessionId: null,
      initialized: false,
      hasMessages: false,
      currentChunk: 0,
      totalChunks: 0,
      lastAccessed: now,
      ...existing,
      ...updates
    }

    this.sessions.set(documentId, sessionState)
  }

  /**
   * Mark session as initialized
   */
  markSessionInitialized(
    documentId: string,
    sessionId: string,
    hasMessages: boolean = false,
    currentChunk: number = 0,
    totalChunks: number = 0
  ): void {
    this.updateSessionState(documentId, {
      sessionId,
      initialized: true,
      hasMessages,
      currentChunk,
      totalChunks
    })
  }

  /**
   * Check if session has existing messages (to avoid reprocessing first chunk)
   */
  hasExistingMessages(documentId: string): boolean {
    const session = this.getSessionState(documentId)
    return session?.hasMessages || false
  }

  /**
   * Clear session state (for logout, document deletion, etc.)
   */
  clearSession(documentId: string): void {
    console.log(`🧹 Clearing session state for document: ${documentId}`)
    this.sessions.delete(documentId)
  }

  /**
   * Clear all sessions
   */
  clearAllSessions(): void {
    this.sessions.clear()
  }

  /**
   * Get all active sessions (for debugging)
   */
  getActiveSessions(): string[] {
    const now = Date.now()
    const activeSessions: string[] = []

    for (const [documentId, session] of this.sessions.entries()) {
      if (now - session.lastAccessed <= this.SESSION_TIMEOUT) {
        activeSessions.push(documentId)
      } else {
        // Clean up expired sessions
        this.sessions.delete(documentId)
      }
    }

    return activeSessions
  }

  /**
   * Update message count for a session
   */
  updateMessageCount(documentId: string, hasMessages: boolean): void {
    const session = this.getSessionState(documentId)
    if (session) {
      this.updateSessionState(documentId, { hasMessages })
    }
  }

  /**
   * Update current chunk for a session
   */
  updateCurrentChunk(documentId: string, currentChunk: number): void {
    const session = this.getSessionState(documentId)
    if (session) {
      this.updateSessionState(documentId, { currentChunk })
    }
  }
}

// Export singleton instance
export const sessionStateManager = new SessionStateManager()

// Export types for use in components
export type { SessionState }