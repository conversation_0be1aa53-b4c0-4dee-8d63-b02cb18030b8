# Database Architecture Notes

## Schema Structure

### Supabase Auth Schema (`auth`)
- `auth.users` - Managed by Supabase Auth
- Handles authentication, email verification, etc.

### Application Schema (`public`) 
- `user` - Custom user data (synced with auth.users via triggers)
- `profiles` - User profile information
- `documents`, `chunks`, `progress` - Application data

## Relationships

### Cross-Schema Relationship (auth.users → public.user)
- **Connection**: Handled by database triggers
- **Cleanup**: Manual deletion from auth.users
- **Note**: Prisma cannot manage this relationship due to cross-schema limitations

### Within Public Schema
- `user` → `profiles` (CASCADE DELETE) ✅
- `user` → `documents` (CASCADE DELETE) ✅  
- `documents` → `chunks` (CASCADE DELETE) ✅
- `user` → `progress` (CASCADE DELETE) ✅

## Current Status
- ✅ Database constraints working correctly
- ✅ Automatic user creation via triggers
- ✅ CASCADE DELETE within public schema
- ⚠️ Prisma schema not fully synced (expected limitation)

## User Lifecycle
1. **Signup**: Creates record in auth.users → trigger creates user + profile
2. **Verification**: Email verification completes → user can access app
3. **Deletion**: Delete from auth.users → manually clean up public schema if needed

## Notes
- This is a common pattern with Supabase + Prisma
- Database is working correctly despite schema sync issues
- Application functions properly for all user operations