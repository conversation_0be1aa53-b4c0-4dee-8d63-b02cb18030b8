"use client"

import { useState, useEffect } from 'react'
import { Wifi, WifiOff, AlertCircle } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'

interface ConnectionStatusProps {
    className?: string
}

export function ConnectionStatus({ className = '' }: ConnectionStatusProps) {
    const [isOnline, setIsOnline] = useState(true)
    const [connectionError, setConnectionError] = useState<string | null>(null)
    const [isRetrying, setIsRetrying] = useState(false)

    useEffect(() => {
        // Check initial connection status
        setIsOnline(navigator.onLine)

        // Listen for online/offline events
        const handleOnline = () => {
            setIsOnline(true)
            setConnectionError(null)
            console.log('🌐 Connection restored')
        }

        const handleOffline = () => {
            setIsOnline(false)
            setConnectionError('No internet connection')
            console.log('🚫 Connection lost')
        }

        window.addEventListener('online', handleOnline)
        window.addEventListener('offline', handleOffline)

        // Test connection to Supabase periodically
        const testConnection = async () => {
            if (!navigator.onLine) return

            try {
                setIsRetrying(true)
                const controller = new AbortController()
                const timeoutId = setTimeout(() => controller.abort(), 5000)

                const response = await fetch('/api/health', {
                    signal: controller.signal,
                    cache: 'no-cache'
                })

                clearTimeout(timeoutId)

                if (response.ok) {
                    setConnectionError(null)
                    setIsOnline(true)
                } else {
                    throw new Error(`Server responded with ${response.status}`)
                }
            } catch (error) {
                console.warn('Connection test failed:', error)
                if (error instanceof Error && error.name === 'AbortError') {
                    setConnectionError('Server connection timeout')
                } else {
                    setConnectionError('Server connection failed')
                }
                setIsOnline(false)
            } finally {
                setIsRetrying(false)
            }
        }

        // Test connection every 30 seconds
        const connectionInterval = setInterval(testConnection, 30000)

        // Initial connection test
        testConnection()

        return () => {
            window.removeEventListener('online', handleOnline)
            window.removeEventListener('offline', handleOffline)
            clearInterval(connectionInterval)
        }
    }, [])

    // Don't show anything if connection is good
    if (isOnline && !connectionError) {
        return null
    }

    return (
        <div className={`fixed top-4 right-4 z-50 ${className}`}>
            <Alert variant="destructive" className="max-w-sm">
                <div className="flex items-center gap-2">
                    {isRetrying ? (
                        <div className="animate-spin">
                            <Wifi className="h-4 w-4" />
                        </div>
                    ) : isOnline ? (
                        <Wifi className="h-4 w-4" />
                    ) : (
                        <WifiOff className="h-4 w-4" />
                    )}
                    <AlertCircle className="h-4 w-4" />
                </div>
                <AlertDescription className="mt-2">
                    {connectionError || 'Connection issues detected'}
                    {isRetrying && (
                        <div className="text-xs mt-1 opacity-75">
                            Reconnecting...
                        </div>
                    )}
                </AlertDescription>
            </Alert>
        </div>
    )
}