/**
 * Error Handling Test Suite
 * Tests the comprehensive error handling and fallback mechanisms
 */

import { fallbackManager } from './fallback-manager'
import { memoryService } from './memory-service'
import { AIErrorHandler, ErrorType } from './error-handler'

export class ErrorHandlingTest {
    /**
     * Test AI provider fallback functionality
     */
    static async testProviderFallback(): Promise<void> {
        console.log('🧪 Testing AI provider fallback...')
        
        try {
            const status = fallbackManager.getStatus()
            console.log('Current AI status:', status)
            
            // Test switching providers
            if (status.availableProviders.length > 1) {
                const originalProvider = status.currentProvider
                const alternativeProvider = status.availableProviders.find(p => p !== originalProvider)
                
                if (alternativeProvider) {
                    const switched = fallbackManager.switchToProvider(alternativeProvider)
                    console.log(`✅ Provider switch test: ${switched ? 'PASSED' : 'FAILED'}`)
                    
                    // Switch back
                    fallbackManager.switchToProvider(originalProvider)
                }
            }
            
            console.log('✅ Provider fallback test completed')
        } catch (error) {
            console.error('❌ Provider fallback test failed:', error)
        }
    }

    /**
     * Test memory service graceful degradation
     */
    static async testMemoryDegradation(): Promise<void> {
        console.log('🧪 Testing memory service degradation...')
        
        try {
            const isAvailable = memoryService.isAvailable()
            console.log(`Memory service available: ${isAvailable}`)
            
            const connectionTest = await memoryService.testConnection()
            console.log(`Memory connection test: ${connectionTest ? 'PASSED' : 'FAILED'}`)
            
            // Test memory operations with graceful degradation
            const testSessionId = 'test-session-123'
            
            await memoryService.addConversationMessage(testSessionId, 'Test message', 'user')
            console.log('✅ Conversation message test completed (with graceful degradation)')
            
            const memories = await memoryService.getAllMemories(testSessionId)
            console.log(`Retrieved ${memories.length} memories (graceful degradation working)`)
            
            console.log('✅ Memory degradation test completed')
        } catch (error) {
            console.error('❌ Memory degradation test failed:', error)
        }
    }

    /**
     * Test error classification
     */
    static testErrorClassification(): void {
        console.log('🧪 Testing error classification...')
        
        const testCases = [
            {
                error: new Error('authentication failed'),
                provider: 'openai',
                expectedType: ErrorType.AUTHENTICATION_ERROR
            },
            {
                error: { status: 429, message: 'rate limit exceeded' },
                provider: 'anthropic',
                expectedType: ErrorType.API_RATE_LIMIT
            },
            {
                error: new Error('network connection failed'),
                provider: 'mem0',
                expectedType: ErrorType.NETWORK_ERROR
            },
            {
                error: new Error('timeout occurred'),
                provider: 'openai',
                expectedType: ErrorType.TIMEOUT_ERROR
            }
        ]

        let passed = 0
        const total = testCases.length

        testCases.forEach((testCase, index) => {
            const classified = AIErrorHandler.classifyError(testCase.error, testCase.provider)
            const success = classified.type === testCase.expectedType
            
            console.log(`Test ${index + 1}: ${success ? '✅ PASSED' : '❌ FAILED'} - ${testCase.expectedType}`)
            
            if (success) passed++
        })

        console.log(`✅ Error classification test: ${passed}/${total} passed`)
    }

    /**
     * Test user-friendly error messages
     */
    static testUserMessages(): void {
        console.log('🧪 Testing user-friendly error messages...')
        
        const errorTypes = [
            ErrorType.PROVIDER_UNAVAILABLE,
            ErrorType.API_RATE_LIMIT,
            ErrorType.AUTHENTICATION_ERROR,
            ErrorType.MEMORY_SERVICE_ERROR,
            ErrorType.NETWORK_ERROR,
            ErrorType.TIMEOUT_ERROR,
            ErrorType.UNKNOWN_ERROR
        ]

        errorTypes.forEach(errorType => {
            const mockError = {
                type: errorType,
                message: `Test ${errorType}`,
                retryable: true,
                fallbackAvailable: true,
                provider: 'test'
            }
            
            const userMessage = AIErrorHandler.getUserMessage(mockError)
            console.log(`${errorType}: "${userMessage}"`)
        })

        console.log('✅ User message test completed')
    }

    /**
     * Run all error handling tests
     */
    static async runAllTests(): Promise<void> {
        console.log('🚀 Starting comprehensive error handling tests...\n')
        
        await this.testProviderFallback()
        console.log('')
        
        await this.testMemoryDegradation()
        console.log('')
        
        this.testErrorClassification()
        console.log('')
        
        this.testUserMessages()
        console.log('')
        
        console.log('🎉 All error handling tests completed!')
    }
}

// Export for use in development/testing
export default ErrorHandlingTest