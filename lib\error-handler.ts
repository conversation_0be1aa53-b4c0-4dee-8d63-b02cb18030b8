import { dbService } from "@/lib/db-service"
import { prisma } from "@/lib/prisma"
import { createClient } from "@/lib/supabase/server"

export interface ErrorDetails {
  code: string
  message: string
  userMessage: string
  retryable: boolean
  requiresCleanup: boolean
}

export class DocumentProcessingError extends Error {
  public readonly code: string
  public readonly userMessage: string
  public readonly retryable: boolean
  public readonly requiresCleanup: boolean

  constructor(details: ErrorDetails) {
    super(details.message)
    this.code = details.code
    this.userMessage = details.userMessage
    this.retryable = details.retryable
    this.requiresCleanup = details.requiresCleanup
    this.name = 'DocumentProcessingError'
  }
}

export class ErrorHandler {
  /**
   * Categorize and create appropriate error based on error message/type
   */
  static categorizeError(error: unknown): DocumentProcessingError {
    if (error instanceof DocumentProcessingError) {
      return error
    }

    const errorMessage = error instanceof Error ? error.message.toLowerCase() : String(error).toLowerCase()

    // API Key errors
    if (errorMessage.includes('api key') || errorMessage.includes('unauthorized')) {
      return new DocumentProcessingError({
        code: 'API_KEY_ERROR',
        message: `API configuration error: ${error instanceof Error ? error.message : error}`,
        userMessage: 'Service configuration error. Please contact support.',
        retryable: false,
        requiresCleanup: true
      })
    }

    // Rate limiting errors
    if (errorMessage.includes('rate limit') || errorMessage.includes('too many requests')) {
      return new DocumentProcessingError({
        code: 'RATE_LIMIT_ERROR',
        message: `Rate limit exceeded: ${error instanceof Error ? error.message : error}`,
        userMessage: 'Processing rate limit exceeded. Please try again in a few minutes.',
        retryable: true,
        requiresCleanup: false
      })
    }

    // Timeout errors
    if (errorMessage.includes('timeout') || errorMessage.includes('timed out')) {
      return new DocumentProcessingError({
        code: 'TIMEOUT_ERROR',
        message: `Processing timeout: ${error instanceof Error ? error.message : error}`,
        userMessage: 'Document processing timed out. The file may be too complex or large.',
        retryable: true,
        requiresCleanup: true
      })
    }

    // File format errors
    if (errorMessage.includes('unsupported') || errorMessage.includes('invalid format')) {
      return new DocumentProcessingError({
        code: 'UNSUPPORTED_FORMAT',
        message: `Unsupported file format: ${error instanceof Error ? error.message : error}`,
        userMessage: 'File format not supported for processing.',
        retryable: false,
        requiresCleanup: true
      })
    }

    // File size errors
    if (errorMessage.includes('file too large') || errorMessage.includes('size limit')) {
      return new DocumentProcessingError({
        code: 'FILE_TOO_LARGE',
        message: `File size error: ${error instanceof Error ? error.message : error}`,
        userMessage: 'File is too large for processing (maximum 50MB).',
        retryable: false,
        requiresCleanup: true
      })
    }

    // Storage errors
    if (errorMessage.includes('storage') || errorMessage.includes('upload') || errorMessage.includes('download')) {
      return new DocumentProcessingError({
        code: 'STORAGE_ERROR',
        message: `Storage error: ${error instanceof Error ? error.message : error}`,
        userMessage: 'File storage error. Please try uploading again.',
        retryable: true,
        requiresCleanup: true
      })
    }

    // Network errors - check first to avoid overlap with database connection errors
    if (errorMessage.includes('network') || errorMessage.includes('fetch') ||
      errorMessage.includes('connection refused') || errorMessage.includes('connection timeout')) {
      return new DocumentProcessingError({
        code: 'NETWORK_ERROR',
        message: `Network error: ${error instanceof Error ? error.message : error}`,
        userMessage: 'Network error. Please check your connection and try again.',
        retryable: true,
        requiresCleanup: false
      })
    }

    // Database errors - more specific connection patterns
    if (errorMessage.includes('database') || errorMessage.includes('prisma') ||
      errorMessage.includes('connection pool')) {
      return new DocumentProcessingError({
        code: 'DATABASE_ERROR',
        message: `Database error: ${error instanceof Error ? error.message : error}`,
        userMessage: 'Database error. Please try again later.',
        retryable: true,
        requiresCleanup: false
      })
    }

    // Generic processing errors
    return new DocumentProcessingError({
      code: 'PROCESSING_ERROR',
      message: `Processing failed: ${error instanceof Error ? error.message : error}`,
      userMessage: 'Document processing failed. Please try again.',
      retryable: true,
      requiresCleanup: true
    })
  }

  /**
   * Handle document processing error with cleanup and database updates
   */
  static async handleDocumentError(
    documentId: string,
    error: unknown,
    userId?: string
  ): Promise<DocumentProcessingError> {
    const processedError = this.categorizeError(error)

    console.error(`[${documentId}] Processing error:`, {
      code: processedError.code,
      message: processedError.message,
      retryable: processedError.retryable,
      requiresCleanup: processedError.requiresCleanup
    })

    try {
      // Update document status in database
      await dbService.document.update({
        where: { id: documentId },
        data: {
          status: 'ERROR',
          errorMessage: processedError.userMessage
        }
      })

      // Perform cleanup if required
      if (processedError.requiresCleanup) {
        await this.cleanupFailedDocument(documentId, userId)
      }
    } catch (cleanupError) {
      console.error(`[${documentId}] Cleanup failed:`, cleanupError)
      // Don't throw cleanup errors, just log them
    }

    return processedError
  }

  /**
   * Clean up resources for failed document processing
   */
  static async cleanupFailedDocument(documentId: string, userId?: string): Promise<void> {
    console.log(`[${documentId}] Starting cleanup for failed document`)

    try {
      // Get document details for cleanup
      const document = await dbService.document.findUnique({
        where: { id: documentId }
      })

      if (!document) {
        console.warn(`[${documentId}] Document not found for cleanup`)
        return
      }

      // Clean up chunks
      const deletedChunks = await dbService.chunk.deleteMany({
        where: { documentId }
      })
      console.log(`[${documentId}] Deleted ${deletedChunks.count} chunks`)

      // Clean up progress records - using prisma directly since dbService doesn't expose deleteMany
      const deletedProgress = await prisma.progress.deleteMany({
        where: { documentId }
      })
      console.log(`[${documentId}] Deleted ${deletedProgress.count} progress records`)

      // Clean up file from storage if we have user ID
      if (userId && document.filePath) {
        try {
          const supabase = await createClient()
          const { error: deleteError } = await supabase.storage
            .from('documents')
            .remove([`${userId}/${document.filePath}`])

          if (deleteError) {
            console.error(`[${documentId}] Failed to delete file from storage:`, deleteError)
          } else {
            console.log(`[${documentId}] Deleted file from storage: ${document.filePath}`)
          }
        } catch (storageError) {
          console.error(`[${documentId}] Storage cleanup error:`, storageError)
        }
      }

      console.log(`[${documentId}] Cleanup completed successfully`)
    } catch (error) {
      console.error(`[${documentId}] Cleanup failed:`, error)
      // Don't throw cleanup errors to maintain consistency with handleDocumentError
      // Cleanup failures shouldn't prevent the main error handling flow
    }
  }

  /**
   * Get authentication token for API calls
   */
  private static async getAuthToken(userId: string): Promise<string | null> {
    try {
      // Get Supabase session token for authentication
      const supabase = await createClient()
      const { data: { session }, error } = await supabase.auth.getSession()

      if (error || !session) {
        console.warn(`[${userId}] No valid session found for authentication`)
        return null
      }

      // Verify the session belongs to the correct user
      if (session.user.id !== userId) {
        console.warn(`[${userId}] Session user mismatch`)
        return null
      }

      return session.access_token
    } catch (error) {
      console.error(`[${userId}] Failed to get auth token:`, error)
      return null
    }
  }

  /**
   * Create retry mechanism for failed documents
   */
  static async retryDocumentProcessing(documentId: string, userId: string): Promise<boolean> {
    try {
      console.log(`[${documentId}] User ${userId} attempting to retry document processing`)

      const document = await dbService.document.findUnique({
        where: {
          id: documentId,
          userId: userId // Ensure user owns the document
        }
      })

      if (!document) {
        throw new Error('Document not found or access denied')
      }

      if (document.status !== 'ERROR') {
        throw new Error('Document is not in error state')
      }

      // Reset document status to PROCESSING
      await dbService.document.update({
        where: { id: documentId },
        data: {
          status: 'PROCESSING',
          errorMessage: null
        }
      })

      // Trigger processing API with proper URL and authentication
      const baseUrl = process.env.NEXT_PUBLIC_APP_URL || (typeof window !== 'undefined' ? window.location.origin : 'http://localhost:3000')
      const authToken = await this.getAuthToken(userId)

      const response = await fetch(`${baseUrl}/api/documents/process`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(authToken && { 'Authorization': `Bearer ${authToken}` })
        },
        body: JSON.stringify({ documentId, userId })
      })

      if (!response.ok) {
        throw new Error('Failed to trigger retry processing')
      }

      console.log(`[${documentId}] Retry processing initiated successfully for user ${userId}`)
      return true
    } catch (error) {
      console.error(`[${documentId}] Retry failed for user ${userId}:`, error)

      // Update document with retry failure
      try {
        await dbService.document.update({
          where: { id: documentId },
          data: {
            status: 'ERROR',
            errorMessage: 'Retry processing failed. Please try uploading again.'
          }
        })
      } catch (updateError) {
        console.error(`[${documentId}] Failed to update document status:`, updateError)
      }

      return false
    }
  }
}
