import { EventEmitter } from 'events'

export interface Job {
  id: string
  type: 'DOCUMENT_PROCESSING'
  data: {
    documentId: string
    userId: string
    fileName: string
    tempFileId: string
    fileSize: number
  }
  priority: number
  createdAt: Date
  attempts: number
  maxAttempts: number
  status: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED'
  error?: string
}

export class JobQueue extends EventEmitter {
  private jobs: Map<string, Job> = new Map()
  private processing: Set<string> = new Set()
  private cleanupTimeouts: Map<string, NodeJS.Timeout> = new Map()
  private workers: number = 3 // Number of concurrent workers
  private activeWorkers: number = 0

  constructor() {
    super()
    this.startWorkers()
  }

  /**
   * Add a job to the queue
   */
  async addJob(jobData: Omit<Job, 'id' | 'createdAt' | 'attempts' | 'status'>): Promise<string> {
    const job: Job = {
      ...jobData,
      id: `job_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      createdAt: new Date(),
      attempts: 0,
      status: 'PENDING'
    }

    this.jobs.set(job.id, job)
    console.log(`[JobQueue] Added job ${job.id} to queue`)

    // Emit event to wake up workers
    this.emit('jobAdded', job)

    return job.id
  }

  /**
   * Get job status
   */
  getJob(jobId: string): Job | undefined {
    return this.jobs.get(jobId)
  }

  /**
   * Get all jobs for a user
   */
  getUserJobs(userId: string): Job[] {
    return Array.from(this.jobs.values()).filter(job => job.data.userId === userId)
  }

  /**
   * Get queue statistics
   */
  getStats() {
    const jobs = Array.from(this.jobs.values())
    return {
      total: jobs.length,
      pending: jobs.filter(j => j.status === 'PENDING').length,
      processing: jobs.filter(j => j.status === 'PROCESSING').length,
      completed: jobs.filter(j => j.status === 'COMPLETED').length,
      failed: jobs.filter(j => j.status === 'FAILED').length,
      activeWorkers: this.activeWorkers
    }
  }

  /**
   * Start worker processes
   */
  private startWorkers() {
    for (let i = 0; i < this.workers; i++) {
      this.startWorker(i)
    }
  }

  /**
   * Start a single worker
   */
  private async startWorker(workerId: number) {
    console.log(`[JobQueue] Starting worker ${workerId}`)
  }

  /**
   * Get the next job to process (priority queue)
   */
  private getNextJob(): Job | null {
    const pendingJobs = Array.from(this.jobs.values())
      .filter(job => job.status === 'PENDING' && !this.processing.has(job.id))
      .sort((a, b) => {
        // Higher priority first, then older jobs first
        if (a.priority !== b.priority) {
          return b.priority - a.priority
        }
        return a.createdAt.getTime() - b.createdAt.getTime()
      })

    return pendingJobs[0] || null
  }

  /**
   * Wait for a new job to be added
   */
  private waitForJob(): Promise<void> {
    return new Promise((resolve) => {
      const timeout = setTimeout(resolve, 5000) // Check every 5 seconds

      const onJobAdded = () => {
        clearTimeout(timeout)
        this.removeListener('jobAdded', onJobAdded)
        resolve()
      }

      this.once('jobAdded', onJobAdded)
    })
  }

  /**
   * Process a single job
   */
  private async processJob(job: Job, workerId: number) {
    console.log(`[JobQueue] Worker ${workerId} processing job ${job.id}`)

    // Mark job as processing
    job.status = 'PROCESSING'
    job.attempts++
    this.processing.add(job.id)
    this.activeWorkers++

    try {
      if (job.type === 'DOCUMENT_PROCESSING') {
        await this.processDocumentJob(job)
      }

      // Job completed successfully
      job.status = 'COMPLETED'
      console.log(`[JobQueue] Worker ${workerId} completed job ${job.id}`)

      // Clean up completed jobs after some time
      const timeoutId = setTimeout(() => {
        this.jobs.delete(job.id)
        this.cleanupTimeouts.delete(job.id)
      }, 5 * 60 * 1000) // Keep for 5 minutes
      this.cleanupTimeouts.set(job.id, timeoutId)

    } catch (error) {
      console.error(`[JobQueue] Worker ${workerId} job ${job.id} failed:`, error)

      job.error = error instanceof Error ? error.message : String(error)

      if (job.attempts >= job.maxAttempts) {
        job.status = 'FAILED'
        console.log(`[JobQueue] Job ${job.id} failed permanently after ${job.attempts} attempts`)
      } else {
        job.status = 'PENDING'
        console.log(`[JobQueue] Job ${job.id} will be retried (attempt ${job.attempts}/${job.maxAttempts})`)
      }
    } finally {
      this.processing.delete(job.id)
      this.activeWorkers--
    }
  }

  /**
   * Process a document processing job
   */
  private async processDocumentJob(job: Job) {
    const { documentId, userId, fileName, tempFileId, fileSize } = job.data

    console.log(`[JobQueue] Processing document ${documentId} (${fileName}, ${fileSize} bytes)`)

    // Import services dynamically to avoid circular dependencies
    const { processDocumentInBackground } = await import('./document-processor')
    const { TempFileService } = await import('./temp-file-service')

    // Retrieve buffer from temporary file
    const buffer = await TempFileService.getBuffer(tempFileId)
    if (!buffer) {
      throw new Error(`Temporary file ${tempFileId} not found or expired`)
    }

    try {
      await processDocumentInBackground(documentId, buffer, fileName, userId)
      console.log(`[JobQueue] Document ${documentId} processed successfully`)
    } finally {
      // Clean up temporary file after processing (success or failure)
      await TempFileService.deleteFile(tempFileId)
      console.log(`[JobQueue] Cleaned up temporary file ${tempFileId}`)
    }
  }

  /**
   * Cancel a job
   */
  cancelJob(jobId: string): boolean {
    const job = this.jobs.get(jobId)

    if (!job) {
      return false
    }

    if (job.status === 'PROCESSING') {
      // Can't cancel a job that's already being processed
      return false
    }

    if (job.status === 'PENDING') {
      job.status = 'FAILED'
      job.error = 'Cancelled by user'
      return true
    }

    return false
  }

  /**
   * Clear all pending cleanup timeouts
   */
  private clearAllTimeouts() {
    this.cleanupTimeouts.forEach(timeout => clearTimeout(timeout))
    this.cleanupTimeouts.clear()
  }

  /**
   * Clear completed and failed jobs
   */
  cleanup() {
    const toDelete: string[] = []

    this.jobs.forEach((job, jobId) => {
      if (job.status === 'COMPLETED' || job.status === 'FAILED') {
        toDelete.push(jobId)
        // Clear any pending timeout for this job
        const timeout = this.cleanupTimeouts.get(jobId)
        if (timeout) {
          clearTimeout(timeout)
          this.cleanupTimeouts.delete(jobId)
        }
      }
    })

    toDelete.forEach(jobId => this.jobs.delete(jobId))

    console.log(`[JobQueue] Cleaned up ${toDelete.length} jobs`)
  }

  /**
   * Shutdown the job queue gracefully
   */
  shutdown() {
    console.log('[JobQueue] Shutting down job queue...')
    this.clearAllTimeouts()
    this.removeAllListeners()
    console.log('[JobQueue] Job queue shutdown complete')
  }
}

// Singleton instance
export const jobQueue = new JobQueue()

// Start automatic cleanup (call this in your app initialization)
export function startAutomaticCleanup(intervalMs: number = 60 * 60 * 1000): NodeJS.Timeout {
  return setInterval(() => {
    jobQueue.cleanup()
  }, intervalMs)
}

// Cleanup every hour
setInterval(() => {
  jobQueue.cleanup()
}, 60 * 60 * 1000)
