import { prisma } from "@/lib/prisma"
// Define types manually to avoid import issues during build
type User = {
  id: string
  email: string
  createdAt: Date
  updatedAt: Date
}

type Profile = {
  id: string
  fullName: string | null
  avatarUrl: string | null
  school: string | null
  createdAt: Date
  updatedAt: Date
  userId: string
}

type Document = {
  id: string
  fileName: string
  filePath: string
  totalChunks: number
  status: string
  errorMessage: string | null
  createdAt: Date
  updatedAt: Date
  userId: string
}

type Chunk = {
  id: string
  documentId: string
  chunkIndex: number
  pageNumber: number | null
  content: string
  createdAt: Date
  updatedAt: Date
  userId: string | null
}

type Progress = {
  id: string
  documentId: string
  currentChunk: number
  sessionId: string | null
  createdAt: Date
  updatedAt: Date
  userId: string
}

// Extended types that include relations
export type UserWithProfile = User & {
  profile: Profile | null
}

export type DocumentWithChunks = Document & {
  chunks: Chunk[]
  progress: Progress[]
}

export type DocumentWithProgress = Document & {
  progress: Progress[]
}

// Database service to replace mock data
export const dbService = {
  // Users
  user: {
    upsert: async (data: { where: { id: string }; update: any; create: any }) => {
      return await prisma.user.upsert({
        where: data.where,
        update: data.update,
        create: data.create,
        include: {
          profile: true
        }
      })
    },

    findUnique: async (where: { id?: string; email?: string }) => {
      return await prisma.user.findUnique({
        where: {
          id: where.id,
          email: where.email
        },
        include: {
          profile: true
        }
      })
    },

    create: async (data: { id: string; email: string; profile?: { fullName: string; school: string; avatarUrl?: string } }) => {
      return await prisma.user.create({
        data: {
          id: data.id,
          email: data.email,
          profile: data.profile ? {
            create: data.profile
          } : undefined
        },
        include: {
          profile: true
        }
      })
    }
  },

  // Documents
  document: {
    create: async (data: { data: Omit<Document, "id" | "createdAt" | "updatedAt"> }) => {
      return await prisma.document.create({
        data: data.data,
        include: {
          chunks: true,
          progress: true
        }
      })
    },

    findMany: async (query: { where: any; include?: any; orderBy?: any; take?: number }) => {
      return await prisma.document.findMany({
        where: query.where,
        include: {
          chunks: query.include?.chunks || false,
          progress: query.include?.progress || true
        },
        orderBy: query.orderBy || { createdAt: 'desc' },
        take: query.take
      })
    },

    findFirst: async (query: { where: any; include?: any }) => {
      return await prisma.document.findFirst({
        where: query.where,
        include: {
          chunks: query.include?.chunks || false,
          progress: query.include?.progress || false
        }
      })
    },

    findUnique: async (query: { where: { id: string; userId?: string }; include?: any }) => {
      return await prisma.document.findUnique({
        where: { id: query.where.id },
        include: {
          chunks: query.include?.chunks || false,
          progress: query.include?.progress || false
        }
      })
    },

    update: async (data: { where: { id: string; userId?: string }; data: Partial<Document> }) => {
      return await prisma.document.update({
        where: data.where,
        data: data.data,
        include: {
          chunks: true,
          progress: true
        }
      })
    },

    delete: async (data: { where: { id: string } }) => {
      // Delete related records first due to foreign key constraints
      await prisma.progress.deleteMany({
        where: { documentId: data.where.id }
      })
      
      await prisma.chunk.deleteMany({
        where: { documentId: data.where.id }
      })

      return await prisma.document.delete({
        where: data.where
      })
    }
  },

  // Chunks
  chunk: {
    create: async (data: { data: Omit<Chunk, "id" | "createdAt" | "updatedAt"> }) => {
      return await prisma.chunk.create({
        data: data.data
      })
    },

    findUnique: async (data: { where: { documentId_chunkIndex: { documentId: string; chunkIndex: number } } }) => {
      return await prisma.chunk.findUnique({
        where: {
          // Note: We need to create a unique constraint in Prisma schema for this to work
          documentId_chunkIndex: data.where.documentId_chunkIndex
        }
      })
    },

    findMany: async (query: { where: { documentId: string }; orderBy?: any }) => {
      return await prisma.chunk.findMany({
        where: query.where,
        orderBy: query.orderBy || { chunkIndex: 'asc' }
      })
    },
    
    deleteMany: async (query: { where: { documentId: string } }) => {
      return await prisma.chunk.deleteMany({
        where: query.where
      })
    }
  },

  // Progress
  progress: {
    findUnique: async (data: { where: { userId_documentId: { userId: string; documentId: string } } }) => {
      return await prisma.progress.findFirst({
        where: {
          userId: data.where.userId_documentId.userId,
          documentId: data.where.userId_documentId.documentId
        }
      })
    },

    create: async (data: { data: Omit<Progress, "id" | "createdAt" | "updatedAt"> }) => {
      return await prisma.progress.create({
        data: data.data
      })
    },

    update: async (data: {
      where: { userId_documentId: { userId: string; documentId: string } }
      data: Partial<Progress>
    }) => {
      return await prisma.progress.updateMany({
        where: {
          userId: data.where.userId_documentId.userId,
          documentId: data.where.userId_documentId.documentId
        },
        data: data.data
      })
    },

    upsert: async (data: {
      where: { userId_documentId: { userId: string; documentId: string } }
      create: Omit<Progress, "id" | "createdAt" | "updatedAt">
      update: Partial<Progress>
    }) => {
      const existing = await prisma.progress.findFirst({
        where: {
          userId: data.where.userId_documentId.userId,
          documentId: data.where.userId_documentId.documentId
        }
      })

      if (existing) {
        return await prisma.progress.update({
          where: { id: existing.id },
          data: data.update
        })
      } else {
        return await prisma.progress.create({
          data: data.create
        })
      }
    }
  }
}

// Helper function to trigger UI updates (for compatibility with existing code)
export function triggerDocumentUpdate() {
  if (typeof window !== "undefined") {
    window.dispatchEvent(new CustomEvent("documentsUpdated"))
  }
}
