/**
 * Memory Batch Processor
 * Optimizes Mem0 API calls by batching operations together
 */

import { MemoryClient } from 'mem0ai'

export interface BatchOperation {
    id: string
    type: 'add' | 'search' | 'delete'
    sessionId: string
    data: any
    resolve: (result: any) => void
    reject: (error: any) => void
    timestamp: number
}

export interface BatchConfig {
    maxBatchSize: number
    flushInterval: number // milliseconds
    maxWaitTime: number // milliseconds
    enableBatching: boolean
}

export class MemoryBatchProcessor {
    private client: MemoryClient | null = null
    private pendingOperations: BatchOperation[] = []
    private batchCounter = 0
    private flushTimer: NodeJS.Timeout | null = null
    private config: BatchConfig

    constructor(client: MemoryClient | null, config?: Partial<BatchConfig>) {
        this.client = client
        this.config = {
            maxBatchSize: 10,
            flushInterval: 100, // 100ms
            maxWaitTime: 1000, // 1 second max wait
            enableBatching: true,
            ...config
        }
    }

    /**
     * Add memory operation to batch
     */
    async addMemory(
        sessionId: string,
        messages: any[],
        metadata: any
    ): Promise<void> {
        if (!this.config.enableBatching || !this.client) {
            // Execute immediately if batching disabled or no client
            if (this.client) {
                await this.client.add(messages, { user_id: sessionId, metadata })
            }
            return
        }

        return new Promise((resolve, reject) => {
            const operation: BatchOperation = {
                id: `add-${++this.batchCounter}`,
                type: 'add',
                sessionId,
                data: { messages, metadata },
                resolve,
                reject,
                timestamp: Date.now()
            }

            this.pendingOperations.push(operation)
            this.scheduleFlush()
        })
    }

    /**
     * Search memory operation to batch
     */
    async searchMemory(
        sessionId: string,
        query: string,
        options?: any
    ): Promise<any[]> {
        if (!this.config.enableBatching || !this.client) {
            // Execute immediately if batching disabled or no client
            if (this.client) {
                return await this.client.search(query, { user_id: sessionId, ...options }) || []
            }
            return []
        }

        return new Promise((resolve, reject) => {
            const operation: BatchOperation = {
                id: `search-${++this.batchCounter}`,
                type: 'search',
                sessionId,
                data: { query, options },
                resolve,
                reject,
                timestamp: Date.now()
            }

            this.pendingOperations.push(operation)
            this.scheduleFlush()
        })
    }

    /**
     * Delete memory operation to batch
     */
    async deleteMemory(
        sessionId: string,
        options?: any
    ): Promise<void> {
        if (!this.config.enableBatching || !this.client) {
            // Execute immediately if batching disabled or no client
            if (this.client) {
                await this.client.deleteAll({ user_id: sessionId, ...options })
            }
            return
        }

        return new Promise((resolve, reject) => {
            const operation: BatchOperation = {
                id: `delete-${++this.batchCounter}`,
                type: 'delete',
                sessionId,
                data: { options },
                resolve,
                reject,
                timestamp: Date.now()
            }

            this.pendingOperations.push(operation)
            this.scheduleFlush()
        })
    }

    /**
     * Schedule batch flush
     */
    private scheduleFlush(): void {
        // Flush immediately if batch is full
        if (this.pendingOperations.length >= this.config.maxBatchSize) {
            this.flush()
            return
        }

        // Schedule flush if not already scheduled
        if (!this.flushTimer) {
            this.flushTimer = setTimeout(() => {
                this.flush()
            }, this.config.flushInterval)
        }

        // Check for operations that have waited too long
        const now = Date.now()
        const hasOldOperations = this.pendingOperations.some(
            op => now - op.timestamp > this.config.maxWaitTime
        )

        if (hasOldOperations) {
            this.flush()
        }
    }

    /**
     * Flush pending operations
     */
    private async flush(): Promise<void> {
        if (this.pendingOperations.length === 0) {
            return
        }

        // Clear the timer
        if (this.flushTimer) {
            clearTimeout(this.flushTimer)
            this.flushTimer = null
        }

        const operations = [...this.pendingOperations]
        this.pendingOperations = []

        console.log(`🚀 Flushing ${operations.length} memory operations`)

        // Group operations by type and session for better batching
        const grouped = this.groupOperations(operations)

        // Process each group
        for (const [groupKey, groupOps] of grouped.entries()) {
            await this.processGroup(groupKey, groupOps)
        }
    }

    /**
     * Group operations by type and session
     */
    private groupOperations(operations: BatchOperation[]): Map<string, BatchOperation[]> {
        const groups = new Map<string, BatchOperation[]>()

        for (const op of operations) {
            const key = `${op.type}-${op.sessionId}`
            
            if (!groups.has(key)) {
                groups.set(key, [])
            }
            
            groups.get(key)!.push(op)
        }

        return groups
    }

    /**
     * Process a group of operations
     */
    private async processGroup(groupKey: string, operations: BatchOperation[]): Promise<void> {
        const [type, sessionId] = groupKey.split('-', 2)

        try {
            switch (type) {
                case 'add':
                    await this.processAddBatch(sessionId, operations)
                    break
                case 'search':
                    await this.processSearchBatch(sessionId, operations)
                    break
                case 'delete':
                    await this.processDeleteBatch(sessionId, operations)
                    break
                default:
                    throw new Error(`Unknown operation type: ${type}`)
            }
        } catch (error) {
            console.error(`❌ Batch processing failed for ${groupKey}:`, error)
            
            // Reject all operations in this group
            operations.forEach(op => op.reject(error))
        }
    }

    /**
     * Process batch of add operations
     */
    private async processAddBatch(sessionId: string, operations: BatchOperation[]): Promise<void> {
        if (!this.client) {
            throw new Error('Memory client not available')
        }

        // Combine all messages from the batch
        const allMessages: any[] = []
        const combinedMetadata: any = {
            batch_size: operations.length,
            batch_timestamp: Date.now()
        }

        for (const op of operations) {
            allMessages.push(...op.data.messages)
            
            // Merge metadata (later operations override earlier ones for conflicts)
            Object.assign(combinedMetadata, op.data.metadata)
        }

        try {
            await this.client.add(allMessages, {
                user_id: sessionId,
                metadata: combinedMetadata
            })

            // Resolve all operations
            operations.forEach(op => op.resolve(undefined))
            
            console.log(`✅ Batched ${operations.length} add operations for session ${sessionId}`)
        } catch (error) {
            throw error
        }
    }

    /**
     * Process batch of search operations
     */
    private async processSearchBatch(sessionId: string, operations: BatchOperation[]): Promise<void> {
        if (!this.client) {
            throw new Error('Memory client not available')
        }

        // Execute searches individually (Mem0 doesn't support batch search)
        // But we can execute them concurrently
        const searchPromises = operations.map(async (op) => {
            try {
                const result = await this.client!.search(op.data.query, {
                    user_id: sessionId,
                    ...op.data.options
                })
                op.resolve(result || [])
            } catch (error) {
                op.reject(error)
            }
        })

        await Promise.allSettled(searchPromises)
        console.log(`✅ Processed ${operations.length} search operations for session ${sessionId}`)
    }

    /**
     * Process batch of delete operations
     */
    private async processDeleteBatch(sessionId: string, operations: BatchOperation[]): Promise<void> {
        if (!this.client) {
            throw new Error('Memory client not available')
        }

        // For delete operations, we typically want to delete all for the session
        // Multiple delete operations for the same session can be combined
        try {
            await this.client.deleteAll({ user_id: sessionId })
            
            // Resolve all operations
            operations.forEach(op => op.resolve(undefined))
            
            console.log(`✅ Batched ${operations.length} delete operations for session ${sessionId}`)
        } catch (error) {
            throw error
        }
    }

    /**
     * Force flush all pending operations
     */
    async forceFlush(): Promise<void> {
        await this.flush()
    }

    /**
     * Get batch processor statistics
     */
    getStats(): {
        pendingOperations: number
        batchesProcessed: number
        averageBatchSize: number
        isFlushScheduled: boolean
    } {
        return {
            pendingOperations: this.pendingOperations.length,
            batchesProcessed: this.batchCounter,
            averageBatchSize: this.batchCounter > 0 ? this.batchCounter / Math.max(1, this.batchCounter) : 0,
            isFlushScheduled: this.flushTimer !== null
        }
    }

    /**
     * Update configuration
     */
    updateConfig(newConfig: Partial<BatchConfig>): void {
        this.config = { ...this.config, ...newConfig }
        console.log('📝 Updated batch processor configuration:', this.config)
    }

    /**
     * Shutdown batch processor
     */
    async shutdown(): Promise<void> {
        console.log('🔌 Shutting down memory batch processor...')
        
        // Flush any pending operations
        await this.forceFlush()
        
        // Clear timer
        if (this.flushTimer) {
            clearTimeout(this.flushTimer)
            this.flushTimer = null
        }
        
        console.log('✅ Memory batch processor shut down')
    }
}

// Export for use in memory service
export default MemoryBatchProcessor
