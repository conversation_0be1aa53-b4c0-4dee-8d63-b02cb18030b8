
<!doctype html>
<html lang="en">

<head>
    <title>Code coverage report for All files</title>
    <meta charset="utf-8" />
    <link rel="stylesheet" href="prettify.css" />
    <link rel="stylesheet" href="base.css" />
    <link rel="shortcut icon" type="image/x-icon" href="favicon.png" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <style type='text/css'>
        .coverage-summary .sorter {
            background-image: url(sort-arrow-sprite.png);
        }
    </style>
</head>
    
<body>
<div class='wrapper'>
    <div class='pad1'>
        <h1>All files</h1>
        <div class='clearfix'>
            
            <div class='fl pad1y space-right2'>
                <span class="strong">28.18% </span>
                <span class="quiet">Statements</span>
                <span class='fraction'>352/1249</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.98% </span>
                <span class="quiet">Branches</span>
                <span class='fraction'>200/667</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">33.84% </span>
                <span class="quiet">Functions</span>
                <span class='fraction'>88/260</span>
            </div>
        
            
            <div class='fl pad1y space-right2'>
                <span class="strong">29.25% </span>
                <span class="quiet">Lines</span>
                <span class='fraction'>344/1176</span>
            </div>
        
            
        </div>
        <p class="quiet">
            Press <em>n</em> or <em>j</em> to go to the next uncovered block, <em>b</em>, <em>p</em> or <em>k</em> for the previous block.
        </p>
        <template id="filterTemplate">
            <div class="quiet">
                Filter:
                <input type="search" id="fileSearch">
            </div>
        </template>
    </div>
    <div class='status-line low'></div>
    <div class="pad1">
<table class="coverage-summary">
<thead>
<tr>
   <th data-col="file" data-fmt="html" data-html="true" class="file">File</th>
   <th data-col="pic" data-type="number" data-fmt="html" data-html="true" class="pic"></th>
   <th data-col="statements" data-type="number" data-fmt="pct" class="pct">Statements</th>
   <th data-col="statements_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="branches" data-type="number" data-fmt="pct" class="pct">Branches</th>
   <th data-col="branches_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="functions" data-type="number" data-fmt="pct" class="pct">Functions</th>
   <th data-col="functions_raw" data-type="number" data-fmt="html" class="abs"></th>
   <th data-col="lines" data-type="number" data-fmt="pct" class="pct">Lines</th>
   <th data-col="lines_raw" data-type="number" data-fmt="html" class="abs"></th>
</tr>
</thead>
<tbody><tr>
	<td class="file low" data-value="config.ts"><a href="config.ts.html">config.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="31" class="abs low">0/31</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="34" class="abs low">0/34</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="4" class="abs low">0/4</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="30" class="abs low">0/30</td>
	</tr>

<tr>
	<td class="file low" data-value="connection-pool.ts"><a href="connection-pool.ts.html">connection-pool.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="108" class="abs low">0/108</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="27" class="abs low">0/27</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="98" class="abs low">0/98</td>
	</tr>

<tr>
	<td class="file low" data-value="context-engineering.ts"><a href="context-engineering.ts.html">context-engineering.ts</a></td>
	<td data-value="10.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 10%"></div><div class="cover-empty" style="width: 90%"></div></div>
	</td>
	<td data-value="10.3" class="pct low">10.3%</td>
	<td data-value="97" class="abs low">10/97</td>
	<td data-value="3.92" class="pct low">3.92%</td>
	<td data-value="51" class="abs low">2/51</td>
	<td data-value="13.33" class="pct low">13.33%</td>
	<td data-value="15" class="abs low">2/15</td>
	<td data-value="11.11" class="pct low">11.11%</td>
	<td data-value="90" class="abs low">10/90</td>
	</tr>

<tr>
	<td class="file low" data-value="context-strategy-selector.ts"><a href="context-strategy-selector.ts.html">context-strategy-selector.ts</a></td>
	<td data-value="6.55" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 6%"></div><div class="cover-empty" style="width: 94%"></div></div>
	</td>
	<td data-value="6.55" class="pct low">6.55%</td>
	<td data-value="61" class="abs low">4/61</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="33" class="abs low">0/33</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="17" class="abs low">0/17</td>
	<td data-value="7.4" class="pct low">7.4%</td>
	<td data-value="54" class="abs low">4/54</td>
	</tr>

<tr>
	<td class="file low" data-value="document-ai-initializer.ts"><a href="document-ai-initializer.ts.html">document-ai-initializer.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="57" class="abs low">0/57</td>
	</tr>

<tr>
	<td class="file low" data-value="educational-prompts.ts"><a href="educational-prompts.ts.html">educational-prompts.ts</a></td>
	<td data-value="41.17" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 41%"></div><div class="cover-empty" style="width: 59%"></div></div>
	</td>
	<td data-value="41.17" class="pct low">41.17%</td>
	<td data-value="34" class="abs low">14/34</td>
	<td data-value="43.47" class="pct low">43.47%</td>
	<td data-value="23" class="abs low">10/23</td>
	<td data-value="30" class="pct low">30%</td>
	<td data-value="10" class="abs low">3/10</td>
	<td data-value="42.42" class="pct low">42.42%</td>
	<td data-value="33" class="abs low">14/33</td>
	</tr>

<tr>
	<td class="file high" data-value="error-handler.ts"><a href="error-handler.ts.html">error-handler.ts</a></td>
	<td data-value="88.7" class="pic high">
	<div class="chart"><div class="cover-fill" style="width: 88%"></div><div class="cover-empty" style="width: 12%"></div></div>
	</td>
	<td data-value="88.7" class="pct high">88.7%</td>
	<td data-value="62" class="abs high">55/62</td>
	<td data-value="75" class="pct medium">75%</td>
	<td data-value="104" class="abs medium">78/104</td>
	<td data-value="100" class="pct high">100%</td>
	<td data-value="8" class="abs high">8/8</td>
	<td data-value="88.33" class="pct high">88.33%</td>
	<td data-value="60" class="abs high">53/60</td>
	</tr>

<tr>
	<td class="file low" data-value="error-handling-test.ts"><a href="error-handling-test.ts.html">error-handling-test.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="60" class="abs low">0/60</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="58" class="abs low">0/58</td>
	</tr>

<tr>
	<td class="file low" data-value="fallback-manager.ts"><a href="fallback-manager.ts.html">fallback-manager.ts</a></td>
	<td data-value="16.3" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 16%"></div><div class="cover-empty" style="width: 84%"></div></div>
	</td>
	<td data-value="16.3" class="pct low">16.3%</td>
	<td data-value="92" class="abs low">15/92</td>
	<td data-value="5.35" class="pct low">5.35%</td>
	<td data-value="56" class="abs low">3/56</td>
	<td data-value="11.76" class="pct low">11.76%</td>
	<td data-value="17" class="abs low">2/17</td>
	<td data-value="16.85" class="pct low">16.85%</td>
	<td data-value="89" class="abs low">15/89</td>
	</tr>

<tr>
	<td class="file low" data-value="memory-batch-processor.ts"><a href="memory-batch-processor.ts.html">memory-batch-processor.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="115" class="abs low">0/115</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="48" class="abs low">0/48</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="25" class="abs low">0/25</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="111" class="abs low">0/111</td>
	</tr>

<tr>
	<td class="file low" data-value="memory-integration.ts"><a href="memory-integration.ts.html">memory-integration.ts</a></td>
	<td data-value="19.04" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 19%"></div><div class="cover-empty" style="width: 81%"></div></div>
	</td>
	<td data-value="19.04" class="pct low">19.04%</td>
	<td data-value="42" class="abs low">8/42</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="8" class="abs low">0/8</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="7" class="abs low">0/7</td>
	<td data-value="19.04" class="pct low">19.04%</td>
	<td data-value="42" class="abs low">8/42</td>
	</tr>

<tr>
	<td class="file medium" data-value="memory-service.ts"><a href="memory-service.ts.html">memory-service.ts</a></td>
	<td data-value="74.86" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 74%"></div><div class="cover-empty" style="width: 26%"></div></div>
	</td>
	<td data-value="74.86" class="pct medium">74.86%</td>
	<td data-value="191" class="abs medium">143/191</td>
	<td data-value="65.45" class="pct medium">65.45%</td>
	<td data-value="110" class="abs medium">72/110</td>
	<td data-value="83.92" class="pct high">83.92%</td>
	<td data-value="56" class="abs high">47/56</td>
	<td data-value="78.85" class="pct medium">78.85%</td>
	<td data-value="175" class="abs medium">138/175</td>
	</tr>

<tr>
	<td class="file medium" data-value="prompts.ts"><a href="prompts.ts.html">prompts.ts</a></td>
	<td data-value="73.33" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 73%"></div><div class="cover-empty" style="width: 27%"></div></div>
	</td>
	<td data-value="73.33" class="pct medium">73.33%</td>
	<td data-value="15" class="abs medium">11/15</td>
	<td data-value="50" class="pct medium">50%</td>
	<td data-value="4" class="abs medium">2/4</td>
	<td data-value="40" class="pct low">40%</td>
	<td data-value="5" class="abs low">2/5</td>
	<td data-value="78.57" class="pct medium">78.57%</td>
	<td data-value="14" class="abs medium">11/14</td>
	</tr>

<tr>
	<td class="file low" data-value="providers.ts"><a href="providers.ts.html">providers.ts</a></td>
	<td data-value="40.38" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 40%"></div><div class="cover-empty" style="width: 60%"></div></div>
	</td>
	<td data-value="40.38" class="pct low">40.38%</td>
	<td data-value="52" class="abs low">21/52</td>
	<td data-value="21.95" class="pct low">21.95%</td>
	<td data-value="41" class="abs low">9/41</td>
	<td data-value="60" class="pct medium">60%</td>
	<td data-value="10" class="abs medium">6/10</td>
	<td data-value="45.65" class="pct low">45.65%</td>
	<td data-value="46" class="abs low">21/46</td>
	</tr>

<tr>
	<td class="file low" data-value="response-cache.ts"><a href="response-cache.ts.html">response-cache.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="94" class="abs low">0/94</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="36" class="abs low">0/36</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="24" class="abs low">0/24</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="89" class="abs low">0/89</td>
	</tr>

<tr>
	<td class="file medium" data-value="stream-buffer.ts"><a href="stream-buffer.ts.html">stream-buffer.ts</a></td>
	<td data-value="77.17" class="pic medium">
	<div class="chart"><div class="cover-fill" style="width: 77%"></div><div class="cover-empty" style="width: 23%"></div></div>
	</td>
	<td data-value="77.17" class="pct medium">77.17%</td>
	<td data-value="92" class="abs medium">71/92</td>
	<td data-value="54.54" class="pct medium">54.54%</td>
	<td data-value="44" class="abs medium">24/44</td>
	<td data-value="85.71" class="pct high">85.71%</td>
	<td data-value="21" class="abs high">18/21</td>
	<td data-value="77.77" class="pct medium">77.77%</td>
	<td data-value="90" class="abs medium">70/90</td>
	</tr>

<tr>
	<td class="file low" data-value="test-providers.ts"><a href="test-providers.ts.html">test-providers.ts</a></td>
	<td data-value="0" class="pic low">
	<div class="chart"><div class="cover-fill" style="width: 0%"></div><div class="cover-empty" style="width: 100%"></div></div>
	</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="43" class="abs low">0/43</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="12" class="abs low">0/12</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="1" class="abs low">0/1</td>
	<td data-value="0" class="pct low">0%</td>
	<td data-value="40" class="abs low">0/40</td>
	</tr>

</tbody>
</table>
</div>
                <div class='push'></div><!-- for sticky footer -->
            </div><!-- /wrapper -->
            <div class='footer quiet pad2 space-top1 center small'>
                Code coverage generated by
                <a href="https://istanbul.js.org/" target="_blank" rel="noopener noreferrer">istanbul</a>
                at 2025-07-29T11:01:49.652Z
            </div>
        <script src="prettify.js"></script>
        <script>
            window.onload = function () {
                prettyPrint();
            };
        </script>
        <script src="sorter.js"></script>
        <script src="block-navigation.js"></script>
    </body>
</html>
    