/**
 * AI Providers Unit Tests
 * Tests for OpenAI and Anthropic provider implementations
 */

// Jest globals are available globally, no need to import
import { OpenAIProvider, AnthropicProvider, AIProviderFactory } from '../providers'
import { ConversationContext } from '../providers'

// Mock the external dependencies
jest.mock('openai')
jest.mock('@anthropic-ai/sdk')

describe('AI Providers', () => {
  let mockContext: ConversationContext

  beforeEach(() => {
    mockContext = {
      userId: 'test-user-123',
      documentId: 'test-doc-456',
      chunkIndex: 0,
      chunkContent: 'Test content for learning',
      sessionId: 'test-session-789',
      currentQuery: 'What is this about?'
    }

    // Clear all mocks
    jest.clearAllMocks()
  })

  describe('OpenAIProvider', () => {
    let provider: OpenAIProvider

    beforeEach(() => {
      // Mock environment variables
      process.env.OPENAI_API_KEY = 'test-openai-key'
      provider = new OpenAIProvider()
    })

    it('should initialize successfully with valid API key', async () => {
      await expect(provider.initialize()).resolves.not.toThrow()
    })

    it('should throw error when API key is missing', async () => {
      delete process.env.OPENAI_API_KEY
      const newProvider = new OpenAIProvider()

      await expect(newProvider.initialize()).rejects.toThrow('OPENAI_API_KEY is not configured')
    })

    it('should have correct provider name', () => {
      expect(provider.name).toBe('openai')
    })

    it('should generate streaming response', async () => {
      // Create a simple mock that returns tokens directly
      const mockGenerateResponse = jest.fn().mockImplementation(async function* () {
        yield 'Hello '
        yield 'world!'
      })

      // Replace the generateResponse method
      provider.generateResponse = mockGenerateResponse

      const responseGenerator = provider.generateResponse('Test prompt', mockContext)
      const responses: string[] = []

      for await (const token of responseGenerator) {
        responses.push(token)
      }

      expect(responses).toEqual(['Hello ', 'world!'])
      expect(mockGenerateResponse).toHaveBeenCalledWith('Test prompt', mockContext)
    })
  })

  describe('AnthropicProvider', () => {
    let provider: AnthropicProvider

    beforeEach(() => {
      process.env.ANTHROPIC_API_KEY = 'test-anthropic-key'
      provider = new AnthropicProvider()
    })

    it('should initialize successfully with valid API key', async () => {
      await expect(provider.initialize()).resolves.not.toThrow()
    })

    it('should throw error when API key is missing', async () => {
      delete process.env.ANTHROPIC_API_KEY
      const newProvider = new AnthropicProvider()

      await expect(newProvider.initialize()).rejects.toThrow('ANTHROPIC_API_KEY is not configured')
    })

    it('should have correct provider name', () => {
      expect(provider.name).toBe('anthropic')
    })

    it('should generate streaming response', async () => {
      // Create a simple mock that returns tokens directly
      const mockGenerateResponse = jest.fn().mockImplementation(async function* () {
        yield 'Hello '
        yield 'world!'
      })

      // Replace the generateResponse method
      provider.generateResponse = mockGenerateResponse

      const responseGenerator = provider.generateResponse('Test prompt', mockContext)
      const responses: string[] = []

      for await (const token of responseGenerator) {
        responses.push(token)
      }

      expect(responses).toEqual(['Hello ', 'world!'])
      expect(mockGenerateResponse).toHaveBeenCalledWith('Test prompt', mockContext)
    })
  })

  describe('AIProviderFactory', () => {
    it('should create OpenAI provider by default', () => {
      process.env.AI_PROVIDER = 'openai'
      const provider = AIProviderFactory.createProvider()
      expect(provider.name).toBe('openai')
    })

    it('should create Anthropic provider when specified', () => {
      process.env.AI_PROVIDER = 'anthropic'
      const provider = AIProviderFactory.createProvider()
      expect(provider.name).toBe('anthropic')
    })

    it('should throw error for unsupported provider', () => {
      process.env.AI_PROVIDER = 'unsupported'
      expect(() => AIProviderFactory.createProvider()).toThrow('Unsupported AI provider: unsupported')
    })

    it('should return correct provider type', () => {
      process.env.AI_PROVIDER = 'openai'
      expect(AIProviderFactory.getProviderType()).toBe('openai')
    })
  })
})