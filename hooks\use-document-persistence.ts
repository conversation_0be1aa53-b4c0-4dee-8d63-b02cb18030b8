import { useState, useEffect } from 'react'

interface DocumentInfo {
  id: string
  name: string
}

interface UseDocumentPersistenceReturn {
  activeDocument: DocumentInfo | null
  setActiveDocument: (document: DocumentInfo | null) => Promise<void>
  clearActiveDocument: () => Promise<void>
  isLoading: boolean
}

/**
 * Hook for managing persistent document state across page refreshes
 * Uses both localStorage and server-side storage for reliability
 */
export function useDocumentPersistence(): UseDocumentPersistenceReturn {
  const [activeDocument, setActiveDocumentState] = useState<DocumentInfo | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Load active document on mount
  useEffect(() => {
    loadActiveDocument()
  }, [])

  const loadActiveDocument = async (retryCount = 0) => {
    const maxRetries = 2
    
    try {
      setIsLoading(true)
      
      // First try server-side active document with timeout
      const controller = new AbortController()
      const timeoutId = setTimeout(() => controller.abort(), 15000) // 15 second timeout
      
      try {
        const response = await fetch('/api/user/active-document', {
          signal: controller.signal
        })
        
        clearTimeout(timeoutId)
        
        if (response.ok) {
          const data = await response.json()
          
          if (data.hasActiveDocument) {
            const documentInfo = {
              id: data.document.id,
              name: data.document.name
            }
            setActiveDocumentState(documentInfo)
            
            // Sync with localStorage
            localStorage.setItem('lastSelectedDocument', JSON.stringify(documentInfo))
            return
          }
        }
      } catch (fetchError) {
        clearTimeout(timeoutId)
        
        // If network error and we have retries left, try again
        if (retryCount < maxRetries && (fetchError instanceof Error && (fetchError.name === 'AbortError' || fetchError.message.includes('fetch')))) {
          console.warn(`Active document fetch failed (attempt ${retryCount + 1}/${maxRetries + 1}), retrying...`)
          setTimeout(() => {
            loadActiveDocument(retryCount + 1)
          }, 2000 * (retryCount + 1))
          return
        }
        
        console.warn('Failed to fetch active document from server, falling back to localStorage:', fetchError)
      }
      
      // Fallback to localStorage
      const stored = localStorage.getItem('lastSelectedDocument')
      if (stored) {
        try {
          const documentInfo = JSON.parse(stored)
          
          // Try to verify document still exists (with shorter timeout)
          try {
            const verifyController = new AbortController()
            const verifyTimeoutId = setTimeout(() => verifyController.abort(), 5000)
            
            const verifyResponse = await fetch(`/api/documents/${documentInfo.id}`, {
              signal: verifyController.signal
            })
            
            clearTimeout(verifyTimeoutId)
            
            if (verifyResponse.ok) {
              setActiveDocumentState(documentInfo)
              
              // Try to update server-side state (don't wait for it)
              updateServerActiveDocument(documentInfo.id).catch(error => {
                console.warn('Failed to sync with server:', error)
              })
            } else {
              // Document no longer exists
              localStorage.removeItem('lastSelectedDocument')
            }
          } catch (verifyError) {
            // If verification fails, still use localStorage data but don't sync
            console.warn('Failed to verify document, using localStorage data anyway:', verifyError)
            setActiveDocumentState(documentInfo)
          }
        } catch (parseError) {
          console.warn('Failed to parse stored document info:', parseError)
          localStorage.removeItem('lastSelectedDocument')
        }
      }
    } catch (error) {
      console.error('Failed to load active document:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const updateServerActiveDocument = async (documentId: string) => {
    try {
      await fetch('/api/user/active-document', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ documentId })
      })
    } catch (error) {
      console.warn('Failed to update server-side active document:', error)
    }
  }

  const setActiveDocument = async (document: DocumentInfo | null) => {
    setActiveDocumentState(document)
    
    if (document) {
      // Update both localStorage and server
      localStorage.setItem('lastSelectedDocument', JSON.stringify(document))
      await updateServerActiveDocument(document.id)
    } else {
      // Clear both localStorage and server
      localStorage.removeItem('lastSelectedDocument')
      // Note: We don't have a DELETE endpoint, but the server will naturally
      // update when a new document is selected
    }
  }

  const clearActiveDocument = async () => {
    await setActiveDocument(null)
  }

  return {
    activeDocument,
    setActiveDocument,
    clearActiveDocument,
    isLoading
  }
}