import { SecurityService, RedisRateLimitStore } from './security-service'

/**
 * Configure rate limiting for production environments
 * 
 * Usage:
 * 1. Install Redis: npm install redis @upstash/redis
 * 2. Set environment variables: REDIS_URL or UPSTASH_REDIS_REST_URL
 * 3. Call configureRateLimit() in your app initialization
 */

export async function configureRateLimit() {
    // Check if we're in production and have Redis configured
    if (process.env.NODE_ENV === 'production' || process.env.REDIS_URL || process.env.UPSTASH_REDIS_REST_URL) {
        try {
            // Option 1: Standard Redis
            if (process.env.REDIS_URL) {
                let redis = null
                try {
                    const { createClient } = await import('redis')
                    redis = createClient({
                        url: process.env.REDIS_URL
                    })

                    await redis.connect()

                    // Test the connection
                    await redis.ping()

                    const redisStore = new RedisRateLimitStore(redis)
                    SecurityService.configureRateLimitStore(redisStore)

                    console.log('✅ Rate limiting configured with Redis')
                    return
                } catch (error) {
                    console.warn('⚠️ Redis package not installed or connection failed. Install with: pnpm add redis')
                    console.warn('Connection error:', error instanceof Error ? error.message : error)
                    console.warn('Falling back to in-memory rate limiting')

                    // Clean up connection on failure
                    if (redis) {
                        try {
                            await redis.disconnect()
                        } catch (disconnectError) {
                            console.warn('Failed to disconnect Redis client:', disconnectError)
                        }
                    }
                }
            }

            // Option 2: Upstash Redis (serverless-friendly)
            if (process.env.UPSTASH_REDIS_REST_URL && process.env.UPSTASH_REDIS_REST_TOKEN) {
                try {
                    const { Redis } = await import('@upstash/redis')
                    const redis = new Redis({
                        url: process.env.UPSTASH_REDIS_REST_URL,
                        token: process.env.UPSTASH_REDIS_REST_TOKEN,
                    })

                    // Test the connection
                    await redis.ping()

                    const redisStore = new RedisRateLimitStore(redis)
                    SecurityService.configureRateLimitStore(redisStore)

                    console.log('✅ Rate limiting configured with Upstash Redis')
                    return
                } catch (error) {
                    console.warn('⚠️ @upstash/redis package not installed or connection failed. Install with: pnpm add @upstash/redis')
                    console.warn('Connection error:', error instanceof Error ? error.message : error)
                    console.warn('Falling back to in-memory rate limiting')
                }
            }

        } catch (error) {
            console.warn('⚠️ Failed to configure Redis rate limiting, falling back to in-memory:', error)
        }
    }

    // Development or fallback: use in-memory store
    console.log('📝 Rate limiting using in-memory store (development mode)')
}

/**
 * Environment variables needed for Redis rate limiting:
 * 
 * Standard Redis:
 * REDIS_URL=redis://localhost:6379
 * 
 * Upstash Redis (recommended for serverless):
 * UPSTASH_REDIS_REST_URL=https://your-redis.upstash.io
 * UPSTASH_REDIS_REST_TOKEN=your-token
 */
