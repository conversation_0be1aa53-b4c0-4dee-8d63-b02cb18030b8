"use client"

import { useState, useEffect } from 'react'
import { WifiOff, RefreshCw } from 'lucide-react'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'

export function OfflineBanner() {
  const [showBanner, setShowBanner] = useState(false)

  useEffect(() => {
    const handleOnline = () => {
      setShowBanner(false)
      // Show brief success message
      setTimeout(() => {
        if (document.querySelector('[data-sonner-toaster]')) {
          const event = new CustomEvent('toast', {
            detail: { type: 'success', message: 'Connection restored!' }
          })
          window.dispatchEvent(event)
        }
      }, 100)
    }

    const handleOffline = () => {
      setShowBanner(true)
    }

    // Set initial state
    setShowBanner(!navigator.onLine)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  const handleRetry = () => {
    window.location.reload()
  }

  if (!showBanner) return null

  return (
    <div className="fixed top-0 left-0 right-0 z-50 bg-destructive text-destructive-foreground">
      <Alert variant="destructive" className="rounded-none border-0">
        <WifiOff className="h-4 w-4" />
        <AlertDescription className="flex items-center justify-between w-full">
          <span>You're offline. Some features may not work properly.</span>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRetry}
            className="ml-4 bg-transparent border-destructive-foreground text-destructive-foreground hover:bg-destructive-foreground hover:text-destructive"
          >
            <RefreshCw className="h-3 w-3 mr-1" />
            Retry
          </Button>
        </AlertDescription>
      </Alert>
    </div>
  )
}