import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'

/**
 * Test endpoint to verify state management fixes
 */
export async function GET(_request: NextRequest) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Test 1: Get user's documents with progress
    const documents = await dbService.document.findMany({
      where: {
        userId: session.user.id
      },
      include: {
        progress: {
          where: {
            userId: session.user.id
          }
        }
      },
      orderBy: {
        updatedAt: 'desc'
      }
    })

    // Test 2: Check for session persistence
    const documentsWithSessions = documents.filter((doc: any) => 
      doc.progress.length > 0 && doc.progress[0].sessionId
    )

    // Test 3: Get chunk counts
    const chunkCounts = await Promise.all(
      documents.map(async (doc: any) => {
        const chunks = await dbService.chunk.findMany({
          where: { documentId: doc.id }
        })
        const chunkCount = chunks.length
        return { documentId: doc.id, chunkCount }
      })
    )

    return NextResponse.json({
      success: true,
      userId: session.user.id,
      tests: {
        totalDocuments: documents.length,
        documentsWithSessions: documentsWithSessions.length,
        documentsWithProgress: documents.filter((doc: any) => doc.progress.length > 0).length,
        chunkCounts,
        lastDocument: documents[0] ? {
          id: documents[0].id,
          name: documents[0].fileName,
          currentChunk: documents[0].progress[0]?.currentChunk || 0,
          sessionId: documents[0].progress[0]?.sessionId || null
        } : null
      },
      documents: documents.map((doc: any) => ({
        id: doc.id,
        name: doc.fileName,
        status: doc.status,
        totalChunks: doc.totalChunks,
        progress: doc.progress[0] ? {
          currentChunk: doc.progress[0].currentChunk,
          sessionId: doc.progress[0].sessionId
        } : null
      }))
    })

  } catch (error) {
    console.error('State test error:', error)
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}
