"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function TestMem0Page() {
  const [testResults, setTestResults] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [sessionId, setSessionId] = useState("")
  const [message, setMessage] = useState("")
  const [query, setQuery] = useState("")

  const runConnectionTest = async () => {
    setLoading(true)
    try {
      const response = await fetch("/api/test-mem0")
      const data = await response.json()
      setTestResults(data)
    } catch (error) {
      setTestResults({ success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  const testAction = async (action: string, payload: any = {}) => {
    setLoading(true)
    try {
      const response = await fetch("/api/test-mem0", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ action, ...payload })
      })
      const data = await response.json()
      setTestResults(data)
    } catch (error) {
      setTestResults({ success: false, error: error instanceof Error ? error.message : 'Unknown error' })
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Mem0 Connection Test</h1>
      
      <div className="grid gap-6">
        {/* Connection Test */}
        <Card>
          <CardHeader>
            <CardTitle>Connection Test</CardTitle>
          </CardHeader>
          <CardContent>
            <Button 
              onClick={runConnectionTest} 
              disabled={loading}
              className="mb-4"
            >
              {loading ? 'Testing...' : 'Test Mem0 Connection'}
            </Button>
          </CardContent>
        </Card>

        {/* Manual Tests */}
        <Card>
          <CardHeader>
            <CardTitle>Manual Tests</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex gap-2">
              <Button 
                onClick={() => testAction('init')}
                disabled={loading}
              >
                Initialize Session
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Input
                placeholder="Session ID"
                value={sessionId}
                onChange={(e) => setSessionId(e.target.value)}
              />
              <Input
                placeholder="Message to add"
                value={message}
                onChange={(e) => setMessage(e.target.value)}
              />
              <Button 
                onClick={() => testAction('add', { sessionId, message })}
                disabled={loading || !sessionId || !message}
              >
                Add Message
              </Button>
            </div>
            
            <div className="flex gap-2">
              <Input
                placeholder="Session ID"
                value={sessionId}
                onChange={(e) => setSessionId(e.target.value)}
              />
              <Input
                placeholder="Search query"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
              />
              <Button 
                onClick={() => testAction('search', { sessionId, query })}
                disabled={loading || !sessionId || !query}
              >
                Search Memories
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Results */}
        {testResults && (
          <Card>
            <CardHeader>
              <CardTitle className={testResults.success ? "text-green-600" : "text-red-600"}>
                Test Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded text-sm overflow-auto">
                {JSON.stringify(testResults, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
