/**
 * <PERSON><PERSON><PERSON> Tests
 * Tests for AI error classification and handling
 */

// Import the error handler directly
import { AIErrorHandler, ErrorType } from '../error-handler'

describe('AIErrorHandler', () => {
  describe('Error Classification', () => {
    it('should classify OpenAI authentication errors', () => {
      const error = { status: 401, message: 'Invalid API key' }
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.AUTHENTICATION_ERROR)
      expect(classified.retryable).toBe(false)
      expect(classified.fallbackAvailable).toBe(true)
      expect(classified.provider).toBe('openai')
    })

    it('should classify rate limit errors', () => {
      const error = { status: 429, message: 'Rate limit exceeded' }
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.API_RATE_LIMIT)
      expect(classified.retryable).toBe(true)
      expect(classified.fallbackAvailable).toBe(true)
    })

    it('should classify network errors', () => {
      const error = new Error('ECONNREFUSED')
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.NETWORK_ERROR)
      expect(classified.retryable).toBe(true)
    })

    it('should classify timeout errors', () => {
      const error = new Error('Request timeout')
      const classified = AIErrorHandler.classifyError(error, 'anthropic')
      
      expect(classified.type).toBe(ErrorType.TIMEOUT_ERROR)
      expect(classified.retryable).toBe(true)
    })

    it('should classify unknown errors', () => {
      const error = new Error('Something weird happened')
      const classified = AIErrorHandler.classifyError(error, 'openai')
      
      expect(classified.type).toBe(ErrorType.UNKNOWN_ERROR)
      expect(classified.retryable).toBe(true)
    })
  })

  describe('User Messages', () => {
    it('should provide user-friendly error messages', () => {
      const authError = {
        type: ErrorType.AUTHENTICATION_ERROR,
        message: 'Auth failed',
        retryable: false,
        fallbackAvailable: true,
        provider: 'openai'
      }
      
      const userMessage = AIErrorHandler.getUserMessage(authError)
      expect(userMessage).toContain('Authentication failed')
    })

    it('should handle different error types', () => {
      const errorTypes = [
        ErrorType.PROVIDER_UNAVAILABLE,
        ErrorType.API_RATE_LIMIT,
        ErrorType.NETWORK_ERROR,
        ErrorType.TIMEOUT_ERROR,
        ErrorType.MEMORY_SERVICE_ERROR
      ]

      errorTypes.forEach(errorType => {
        const mockError = {
          type: errorType,
          message: `Test ${errorType}`,
          retryable: true,
          fallbackAvailable: true,
          provider: 'test'
        }
        
        const userMessage = AIErrorHandler.getUserMessage(mockError)
        expect(userMessage).toBeDefined()
        expect(typeof userMessage).toBe('string')
        expect(userMessage.length).toBeGreaterThan(0)
      })
    })
  })

  describe('Fallback Logic', () => {
    it('should determine when to fallback', () => {
      const authError = {
        type: ErrorType.AUTHENTICATION_ERROR,
        message: 'Auth failed',
        retryable: false,
        fallbackAvailable: true,
        provider: 'openai'
      }
      
      expect(AIErrorHandler.shouldFallback(authError)).toBe(true)
      
      const networkError = {
        type: ErrorType.NETWORK_ERROR,
        message: 'Network failed',
        retryable: true,
        fallbackAvailable: false,
        provider: 'openai'
      }
      
      expect(AIErrorHandler.shouldFallback(networkError)).toBe(false)
    })
  })

  describe('Retry Logic', () => {
    it('should retry operations with exponential backoff', async () => {
      let attempts = 0
      const operation = jest.fn().mockImplementation(() => {
        attempts++
        if (attempts < 3) {
          throw new Error('Temporary failure')
        }
        return 'success'
      })

      const result = await AIErrorHandler.withRetry(operation, 'test', 3)
      
      expect(result).toBe('success')
      expect(attempts).toBe(3)
    })

    it('should stop retrying non-retryable errors', async () => {
      const operation = jest.fn().mockImplementation(() => {
        const error = new Error('Authentication failed')
        throw error
      })

      try {
        await AIErrorHandler.withRetry(operation, 'openai', 3)
        fail('Expected function to throw')
      } catch (error) {
        expect(error).toBeDefined()
        expect(operation).toHaveBeenCalledTimes(1) // Should not retry auth errors
      }
    })
  })
})