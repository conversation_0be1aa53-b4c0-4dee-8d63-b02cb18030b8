# AI Chat Integration Design

## Overview

The AI chat integration feature builds on top of the existing file upload and chunking system to provide immediate AI-powered tutoring. The system automatically initializes conversations when documents become "READY", supports both OpenAI and Anthropic APIs, implements streaming responses, and uses Mem0 for conversation memory management.

## Architecture

### Current System Integration Points
The AI integration connects to the existing system at these points:
- **Document Processing**: Triggers after LlamaParse completes and document status becomes "READY"
- **Dashboard UI**: Uses existing chat interface and chunk navigation
- **Database**: Extends existing Message/Conversation models
- **Chunk Management**: Integrates with existing chunk retrieval system

### AI Service Architecture
```mermaid
graph TD
    A[Document Status: READY] --> B[Retrieve First Chunk]
    B --> C[Initialize Mem0 Session]
    C --> D[Select AI Provider]
    D --> E{Provider Type}
    E -->|OpenAI| F[OpenAI API Client]
    E -->|Anthropic| G[Anthropic API Client]
    F --> H[Stream Response]
    G --> H[Stream Response]
    H --> I[Update Mem0 Memory]
    I --> J[Store in Database]
    J --> K[Display in UI]
```

### Memory Management with Mem0
```mermaid
graph TD
    A[User-Document Session] --> B[Mem0 Memory Instance]
    B --> C[Conversation Context]
    B --> D[Chunk History]
    B --> E[Learning Progress]
    C --> F[AI Response Generation]
    D --> F
    E --> F
    F --> G[Update Memory]
    G --> B
```

## Components and Interfaces

### AI Service Layer

#### AI Provider Interface
```typescript
interface AIProvider {
  name: 'openai' | 'anthropic'
  generateResponse(prompt: string, context: ConversationContext): AsyncGenerator<string>
  initialize(config: AIConfig): Promise<void>
}

interface ConversationContext {
  userId: string
  documentId: string
  chunkIndex: number
  chunkContent: string
  conversationHistory: Message[]
  memoryContext: Mem0Context
}
```

#### OpenAI Integration
```typescript
import OpenAI from 'openai'

class OpenAIProvider implements AIProvider {
  private client: OpenAI
  
  async *generateResponse(prompt: string, context: ConversationContext) {
    const stream = await this.client.chat.completions.create({
      model: 'gpt-4',
      messages: this.buildMessages(prompt, context),
      stream: true,
      temperature: 0.7
    })
    
    for await (const chunk of stream) {
      const content = chunk.choices[0]?.delta?.content
      if (content) yield content
    }
  }
}
```

#### Anthropic Integration
```typescript
import Anthropic from '@anthropic-ai/sdk'

class AnthropicProvider implements AIProvider {
  private client: Anthropic
  
  async *generateResponse(prompt: string, context: ConversationContext) {
    const stream = await this.client.messages.create({
      model: 'claude-3-sonnet-20240229',
      messages: this.buildMessages(prompt, context),
      stream: true,
      max_tokens: 1000
    })
    
    for await (const chunk of stream) {
      if (chunk.type === 'content_block_delta') {
        yield chunk.delta.text
      }
    }
  }
}
```

### Mem0 Integration

#### Advanced Memory Service using Mem0 v2 with Context Engineering
```typescript
import { MemoryClient } from 'mem0ai'

class AdvancedMemoryService {
  private client: MemoryClient
  
  constructor() {
    this.client = new MemoryClient({
      apiKey: process.env.MEM0_API_KEY,
      version: 'v2'
    })
  }
  
  async initializeSession(userId: string, documentId: string): Promise<string> {
    const sessionId = `${userId}-${documentId}`
    
    // Initialize with custom categories for educational context
    await this.client.add({
      messages: [{
        role: 'system',
        content: 'Starting new tutoring session for document'
      }],
      user_id: sessionId,
      agent_id: 'guided-tutor',
      custom_categories: ['session_start', 'document_context'],
      custom_instructions: `
        Focus on educational context. Store learning progress, misconceptions, 
        and understanding levels. Use Bloom's Taxonomy levels for categorization.
      `
    })
    
    // Update Progress table with sessionId
    await prisma.progress.upsert({
      where: {
        userId_documentId: {
          userId,
          documentId,
          sessionId
        }
      },
      update: { sessionId },
      create: {
        userId,
        documentId,
        sessionId,
        currentChunk: 0
      }
    })
    
    return sessionId
  }
  
  async addLearningMemory(sessionId: string, content: string, category: string, bloomLevel: string) {
    await this.client.add({
      messages: [{ role: 'system', content }],
      user_id: sessionId,
      agent_id: 'guided-tutor',
      custom_categories: [category, bloomLevel, 'learning_progress'],
      metadata: {
        bloom_level: bloomLevel,
        category: category,
        timestamp: new Date().toISOString()
      }
    })
  }
  
  async addConceptUnderstanding(sessionId: string, concept: string, understandingLevel: number, evidence: string) {
    await this.client.add({
      messages: [{
        role: 'system',
        content: `Concept: ${concept} | Understanding Level: ${understandingLevel}/5 | Evidence: ${evidence}`
      }],
      user_id: sessionId,
      agent_id: 'guided-tutor',
      custom_categories: ['concept_understanding', 'assessment'],
      metadata: {
        concept,
        understanding_level: understandingLevel,
        evidence
      }
    })
  }
  
  async addMisconception(sessionId: string, misconception: string, correction: string) {
    await this.client.add({
      messages: [{
        role: 'system',
        content: `Misconception: ${misconception} | Correction: ${correction}`
      }],
      user_id: sessionId,
      agent_id: 'guided-tutor',
      custom_categories: ['misconception', 'correction'],
      metadata: {
        misconception,
        correction,
        needs_reinforcement: true
      }
    })
  }
  
  async getContextualMemory(sessionId: string, query: string, bloomLevel?: string): Promise<any[]> {
    // Use criteria-based retrieval for educational relevance
    const criteria = [
      {
        name: 'educational_relevance',
        description: 'How relevant is this memory to current learning objective',
        weight: 0.4
      },
      {
        name: 'understanding_level',
        description: 'Match with student current understanding level',
        weight: 0.3
      },
      {
        name: 'recency',
        description: 'How recent is this learning interaction',
        weight: 0.2
      },
      {
        name: 'misconception_risk',
        description: 'Risk of reinforcing misconceptions',
        weight: 0.1
      }
    ]
    
    const searchParams = {
      query,
      user_id: sessionId,
      agent_id: 'guided-tutor',
      criteria,
      advanced_retrieval: {
        keyword_search: true,
        ranking_function: 'educational_priority'
      },
      limit: 5
    }
    
    if (bloomLevel) {
      searchParams.filters = {
        bloom_level: bloomLevel
      }
    }
    
    return await this.client.search(searchParams)
  }
  
  async getConceptProgress(sessionId: string, concept: string): Promise<any[]> {
    return await this.client.search({
      query: concept,
      user_id: sessionId,
      agent_id: 'guided-tutor',
      filters: {
        category: 'concept_understanding'
      },
      advanced_retrieval: {
        keyword_search: true
      }
    })
  }
  
  async getMisconceptions(sessionId: string): Promise<any[]> {
    return await this.client.search({
      query: 'misconception correction',
      user_id: sessionId,
      agent_id: 'guided-tutor',
      filters: {
        category: 'misconception',
        needs_reinforcement: true
      }
    })
  }
  
  async buildEducationalContext(sessionId: string, currentQuery: string, chunkContent: string): Promise<string> {
    // Get contextually relevant memories
    const relevantMemories = await this.getContextualMemory(sessionId, currentQuery)
    
    // Get any misconceptions that need addressing
    const misconceptions = await this.getMisconceptions(sessionId)
    
    // Build focused educational context
    let context = `CURRENT CONTENT:\n${chunkContent}\n\n`
    
    if (relevantMemories.length > 0) {
      context += `RELEVANT LEARNING CONTEXT:\n`
      relevantMemories.forEach(memory => {
        context += `- ${memory.memory} (Relevance: ${memory.score})\n`
      })
      context += `\n`
    }
    
    if (misconceptions.length > 0) {
      context += `MISCONCEPTIONS TO ADDRESS:\n`
      misconceptions.forEach(misc => {
        context += `- ${misc.memory}\n`
      })
      context += `\n`
    }
    
    context += `STUDENT QUESTION: ${currentQuery}\n`
    
    return context
  }
}
```

### API Endpoints

#### Chat Initialization Endpoint
```typescript
// POST /api/chat/initialize
interface InitializeRequest {
  documentId: string
}

interface InitializeResponse {
  sessionId: string
  messages: Message[]
  currentChunk: number
  totalChunks: number
}
```

#### Streaming Chat Endpoint
```typescript
// POST /api/chat/stream
interface StreamRequest {
  documentId: string
  chunkIndex: number
  message: string
  provider: 'openai' | 'anthropic'
}

// Returns Server-Sent Events stream
interface StreamResponse {
  type: 'token' | 'complete' | 'error'
  content: string
  messageId?: string
}
```

#### Chunk Navigation Endpoint
```typescript
// POST /api/chat/next-chunk
interface NextChunkRequest {
  documentId: string
  currentChunk: number
}

interface NextChunkResponse {
  currentChunk: number
  chunkContent: string
  aiResponse: string // Pre-generated response for new chunk
  messages: Message[]
}
```

## Data Models

### Existing Schema Usage
The AI integration uses your existing database models without modifications:

- **Document**: Already has `status` field to track "PROCESSING" | "READY" | "ERROR"
- **Chunk**: Contains the content that gets sent to AI services
- **Progress**: Already has `sessionId` field which will be used for Mem0 session tracking
- **User**: For authentication and user-specific sessions

### AI Configuration (Environment Variables)
Instead of database storage, AI configuration is handled through environment variables:
```env
# AI Provider Selection
AI_PROVIDER=openai  # or 'anthropic'

# OpenAI Configuration
OPENAI_API_KEY=your_openai_key
OPENAI_MODEL=gpt-4
OPENAI_TEMPERATURE=0.7
OPENAI_MAX_TOKENS=1000

# Anthropic Configuration
ANTHROPIC_API_KEY=your_anthropic_key
ANTHROPIC_MODEL=claude-3-sonnet-20240229
ANTHROPIC_TEMPERATURE=0.7
ANTHROPIC_MAX_TOKENS=1000

# Mem0 Configuration
MEM0_API_KEY=your_mem0_key
```
```

## Educational Context Engineering with Bloom's Taxonomy

### Bloom's Taxonomy Integration
```typescript
enum BloomLevel {
  REMEMBER = 'remember',
  UNDERSTAND = 'understand', 
  APPLY = 'apply',
  ANALYZE = 'analyze',
  EVALUATE = 'evaluate',
  CREATE = 'create'
}

const BLOOM_PROMPTS = {
  [BloomLevel.REMEMBER]: 'Help student recall and recognize key facts and concepts',
  [BloomLevel.UNDERSTAND]: 'Guide student to explain ideas and concepts in their own words',
  [BloomLevel.APPLY]: 'Encourage student to use knowledge in new situations',
  [BloomLevel.ANALYZE]: 'Help student break down information and examine relationships',
  [BloomLevel.EVALUATE]: 'Guide student to make judgments and defend positions',
  [BloomLevel.CREATE]: 'Encourage student to combine elements in new ways'
}
```

### Advanced Prompt Engineering
```typescript
const ADVANCED_TUTORING_PROMPT = `
You are an expert AI tutor using the Socratic method and Bloom's Taxonomy to prevent illusionary knowledge.

EDUCATIONAL CONTEXT:
{educationalContext}

CURRENT BLOOM LEVEL: {currentBloomLevel}
LEARNING OBJECTIVE: {bloomPrompts[currentBloomLevel]}

ANTI-ILLUSIONARY KNOWLEDGE STRATEGIES:
1. Ask students to explain concepts in their own words
2. Request examples from their own experience
3. Challenge assumptions and probe deeper understanding
4. Identify and correct misconceptions immediately
5. Ensure application before moving to higher-order thinking

SOCRATIC METHOD GUIDELINES:
- Ask probing questions rather than giving direct answers
- Build on student responses with follow-up questions
- Guide discovery through questioning
- Encourage critical thinking and self-reflection

MISCONCEPTIONS TO ADDRESS:
{misconceptions}

STUDENT'S CURRENT UNDERSTANDING LEVEL:
{understandingContext}

Respond with a question or guidance that moves the student toward genuine understanding.
`
```

### Chunk Transition Prompt
```typescript
const CHUNK_TRANSITION_PROMPT = `
The student is now moving to chunk {newChunkIndex} of {totalChunks}.

Previous learning: {previousContext}
New content: {newChunkContent}

Acknowledge the progression, briefly connect to previous learning, and introduce the new section with an engaging question.
`
```

## Streaming Implementation

### Frontend Streaming Handler
```typescript
class StreamingChatHandler {
  private eventSource: EventSource | null = null
  
  async sendMessage(message: string, onToken: (token: string) => void, onComplete: () => void) {
    const response = await fetch('/api/chat/stream', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        documentId: this.documentId,
        chunkIndex: this.currentChunk,
        message,
        provider: this.selectedProvider
      })
    })
    
    const reader = response.body?.getReader()
    const decoder = new TextDecoder()
    
    while (true) {
      const { done, value } = await reader!.read()
      if (done) break
      
      const chunk = decoder.decode(value)
      const lines = chunk.split('\n')
      
      for (const line of lines) {
        if (line.startsWith('data: ')) {
          const data = JSON.parse(line.slice(6))
          
          if (data.type === 'token') {
            onToken(data.content)
          } else if (data.type === 'complete') {
            onComplete()
            break
          }
        }
      }
    }
  }
}
```

### Backend Streaming Response
```typescript
export async function POST(request: Request) {
  const { documentId, chunkIndex, message, provider } = await request.json()
  
  const encoder = new TextEncoder()
  const stream = new ReadableStream({
    async start(controller) {
      try {
        const aiProvider = getAIProvider(provider)
        const context = await buildContext(documentId, chunkIndex)
        
        let fullResponse = ''
        
        for await (const token of aiProvider.generateResponse(message, context)) {
          fullResponse += token
          
          const data = JSON.stringify({ type: 'token', content: token })
          controller.enqueue(encoder.encode(`data: ${data}\n\n`))
        }
        
        // Store complete message in database
        await storeMessage(documentId, fullResponse, 'assistant')
        
        // Update Mem0 memory
        await memoryService.addToMemory(context.sessionId, fullResponse, 'assistant')
        
        const completeData = JSON.stringify({ type: 'complete', content: fullResponse })
        controller.enqueue(encoder.encode(`data: ${completeData}\n\n`))
        
      } catch (error) {
        const errorData = JSON.stringify({ type: 'error', content: error.message })
        controller.enqueue(encoder.encode(`data: ${errorData}\n\n`))
      } finally {
        controller.close()
      }
    }
  })
  
  return new Response(stream, {
    headers: {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    }
  })
}
```

## Error Handling

### AI Provider Fallback
```typescript
class AIServiceManager {
  private providers: AIProvider[]
  private currentProvider: number = 0
  
  async generateResponse(prompt: string, context: ConversationContext): Promise<AsyncGenerator<string>> {
    for (let i = 0; i < this.providers.length; i++) {
      try {
        return this.providers[this.currentProvider].generateResponse(prompt, context)
      } catch (error) {
        console.error(`Provider ${this.providers[this.currentProvider].name} failed:`, error)
        this.currentProvider = (this.currentProvider + 1) % this.providers.length
        
        if (i === this.providers.length - 1) {
          throw new Error('All AI providers failed')
        }
      }
    }
  }
}
```

### Memory Service Error Handling
```typescript
class RobustMemoryService extends MemoryService {
  async addToMemory(sessionId: string, message: string, role: 'user' | 'assistant') {
    try {
      await super.addToMemory(sessionId, message, role)
    } catch (error) {
      console.error('Mem0 storage failed, falling back to database only:', error)
      // Continue without memory enhancement
    }
  }
}
```

## Performance Optimization

### Caching Strategy
- Cache AI provider configurations
- Cache Mem0 session data for active conversations
- Implement response caching for similar queries within same chunk

### Connection Management
- Reuse AI provider connections
- Implement connection pooling for database operations
- Optimize Mem0 client connections

### Streaming Optimization
- Implement token batching for smoother UI updates
- Use compression for streaming responses
- Implement client-side buffering for network resilience

## Security Considerations

### API Key Management
- Store API keys in secure environment variables
- Implement key rotation mechanisms
- Monitor API usage and implement rate limiting

### User Data Protection
- Encrypt conversation data at rest
- Implement proper access controls for Mem0 sessions
- Ensure user data isolation in memory storage

### Input Validation
- Sanitize all user inputs before sending to AI providers
- Implement content filtering for inappropriate requests
- Validate chunk content before processing