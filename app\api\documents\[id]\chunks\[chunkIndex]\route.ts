import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'

/**
 * GET /api/documents/[id]/chunks/[chunkIndex]
 * Get a specific chunk from a document
 */
export async function GET(
    _request: NextRequest,
    { params }: { params: { id: string; chunkIndex: string } }
) {
    try {
        const supabase = await createClient()
        const {
            data: { user },
            error: authError,
        } = await supabase.auth.getUser()

        if (authError || !user) {
            return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
        }

        const { id: documentId, chunkIndex: chunkIndexStr } = params
        const chunkIndex = parseInt(chunkIndexStr, 10)

        if (isNaN(chunkIndex) || chunkIndex < 0) {
            return NextResponse.json(
                { error: 'Invalid chunk index' },
                { status: 400 }
            )
        }

        // Verify document exists and belongs to user
        const document = await dbService.document.findFirst({
            where: {
                id: documentId,
                userId: user.id
            }
        })

        if (!document) {
            return NextResponse.json(
                { error: 'Document not found or access denied' },
                { status: 404 }
            )
        }

        // Verify chunk index is within bounds
        if (chunkIndex >= document.totalChunks) {
            return NextResponse.json(
                { error: 'Chunk index out of bounds' },
                { status: 400 }
            )
        }

        // Get the specific chunk
        const chunk = await dbService.chunk.findUnique({
            where: {
                documentId_chunkIndex: {
                    documentId,
                    chunkIndex
                }
            }
        })

        if (!chunk) {
            return NextResponse.json(
                { error: 'Chunk not found' },
                { status: 404 }
            )
        }

        return NextResponse.json({
            id: chunk.id,
            documentId: chunk.documentId,
            chunkIndex: chunk.chunkIndex,
            content: chunk.content,
            pageNumber: chunk.pageNumber,
            createdAt: chunk.createdAt
        })

    } catch (error) {
        console.error('Get chunk API error:', error)
        return NextResponse.json(
            {
                error: 'Internal server error',
                details: error instanceof Error ? error.message : 'Unknown error'
            },
            { status: 500 }
        )
    }
}