"use client"

import { useState, Suspense } from "react"
import { useSearchParams } from "next/navigation"
import { LoginForm } from "@/components/auth/login-form"
import { SignupForm } from "@/components/auth/signup-form"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { CheckCircle } from "lucide-react"
import { GraduationCap, Brain, BookOpen } from "lucide-react"

function AuthContent() {
  const searchParams = useSearchParams()
  const mode = searchParams.get('mode')
  const verified = searchParams.get('verified')
  const [isLogin, setIsLogin] = useState(mode !== 'signup')
  const [showVerifiedMessage] = useState(verified === 'true')

  return (
    <div className="min-h-screen bg-background text-foreground flex items-center justify-center py-8 px-4">
      <div className="container mx-auto flex flex-col lg:flex-row items-center justify-center gap-12">
        {/* Left side - Branding */}
        <div className="flex-1 max-w-lg text-center lg:text-left">
          <div className="flex items-center justify-center lg:justify-start gap-3 mb-6">
            <div className="p-3 bg-primary rounded-xl">
              <GraduationCap className="h-8 w-8 text-primary-foreground" />
            </div>
            <h1 className="text-3xl font-bold text-primary font-heading">Guided Tutor</h1>
          </div>

          <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6 leading-tight font-heading">
            Transform Your Learning with <span className="text-primary">AI-Powered Tutoring</span>
          </h2>

          <p className="text-xl text-muted-foreground mb-8 leading-relaxed font-body">
            Upload your study materials and get personalized, interactive tutoring that adapts to your learning pace and
            style.
          </p>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
            <div className="flex items-start gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Brain className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground font-heading">Smart Analysis</h3>
                <p className="text-sm text-muted-foreground font-body">
                  AI breaks down complex materials into digestible chunks
                </p>
              </div>
            </div>

            <div className="flex items-start gap-3">
              <div className="p-2 bg-primary/10 rounded-lg">
                <BookOpen className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h3 className="font-semibold text-foreground font-heading">Interactive Learning</h3>
                <p className="text-sm text-muted-foreground font-body">
                  Socratic questioning ensures deep understanding
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Right side - Auth Form */}
        <div className="flex-1 max-w-md w-full">
          {/* Email Verification Success Message */}
          {showVerifiedMessage && (
            <Alert className="mb-6 border-green-200 bg-green-50">
              <CheckCircle className="h-4 w-4 text-green-600" />
              <AlertDescription className="text-green-800">
                ✅ Email verified successfully! You can now sign in to your account.
              </AlertDescription>
            </Alert>
          )}

          {isLogin ? (
            <LoginForm onToggleMode={() => setIsLogin(false)} />
          ) : (
            <SignupForm onToggleMode={() => setIsLogin(true)} />
          )}
        </div>
      </div>
    </div>
  )
}

export default function AuthPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Loading...</p>
        </div>
      </div>
    }>
      <AuthContent />
    </Suspense>
  )
}
