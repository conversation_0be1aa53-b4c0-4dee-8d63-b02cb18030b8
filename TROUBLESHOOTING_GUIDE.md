# Troubleshooting Guide - Common Issues & Solutions

## 🚨 Module Resolution Errors

### Error: `Module not found: Can't resolve '@/lib/supabase'`
**Solution:** Use correct import paths:
```typescript
// ✅ Correct
import { createClient } from "@/lib/supabase/client"
import { createClient } from "@/lib/supabase/server"

// ❌ Wrong  
import { createClient } from "@/lib/supabase"
```

## 🗄️ Database Issues

### Error: `Foreign key constraint violation`
**Cause:** Trying to delete records with dependent data
**Solution:** Check CASCADE DELETE constraints are properly set up

### Error: `Cross schema references not allowed`
**Cause:** Prisma detecting auth.users → public.user relationship
**Solution:** This is expected - database works fine, just can't sync schema

### User Deletion Not Working
**Check:** 
1. Deletion trigger exists: `on_auth_user_deleted`
2. CASCADE constraints exist within public schema
3. Delete from `auth.users` (not public.user directly)

## 🔐 Authentication Issues

### Email Verification Not Working
**Check:**
1. SendGrid SMTP configured in Supabase
2. Email templates set up
3. Callback route handling verification correctly

### Users Redirected to Wrong Page
**Check:**
1. CTA buttons point to `/auth?mode=signup`
2. Callback route redirects to `/auth?verified=true`
3. Auth page shows success message

## 🛠️ Prisma Issues

### Error: `EPERM: operation not permitted`
**Cause:** Windows file permission issue
**Solution:** 
1. Close all applications
2. Run as administrator
3. Or restart computer

### Schema Out of Sync
**Expected:** Due to cross-schema references
**Action:** Continue working - database functions correctly

## 📧 Email Issues

### Emails Not Arriving
**Check:**
1. Spam folder
2. SendGrid activity logs
3. Supabase auth logs
4. Try resend functionality

### Slow Email Delivery
**Cause:** SendGrid free tier limitations
**Solution:** Wait 5-15 minutes or upgrade SendGrid plan

## 🔧 Quick Fixes

### Reset Database State
```sql
-- Clean up test data
DELETE FROM public.profiles;
DELETE FROM public.user;
-- Don't delete from auth.users - let triggers handle it
```

### Regenerate Prisma Client
```bash
npx prisma generate
```

### Check Database Constraints
```sql
-- See all foreign key constraints
SELECT tc.table_name, tc.constraint_name, rc.delete_rule
FROM information_schema.table_constraints AS tc 
LEFT JOIN information_schema.referential_constraints AS rc
    ON tc.constraint_name = rc.constraint_name
WHERE tc.constraint_type = 'FOREIGN KEY' 
    AND tc.table_schema = 'public';
```

## 🆘 Emergency Commands

### If Everything Breaks
1. Check `PROJECT_SETUP_SUMMARY.md` for current working state
2. Verify environment variables in `.env`
3. Check Supabase dashboard for auth settings
4. Restart development server

### Database Recovery
If you need to recreate triggers:
```sql
-- Run the complete supabase-setup.sql file
-- Contains all triggers and functions
```

## 📞 Getting Help in New Chat

When starting a new chat, provide:
1. `PROJECT_SETUP_SUMMARY.md` content
2. Specific error message
3. What you were trying to do
4. Current file structure (if relevant)

This ensures continuity without losing context!