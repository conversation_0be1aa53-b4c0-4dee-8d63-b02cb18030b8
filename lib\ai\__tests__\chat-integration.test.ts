/**
 * Chat Integration Tests
 * Tests for complete chat workflow including streaming and error scenarios
 */

// Jest globals are available globally, no need to import
import { NextRequest } from 'next/server'

// Mock all external dependencies
jest.mock('@/lib/supabase/server')
jest.mock('@/lib/db-service')
jest.mock('@/lib/ai/memory-service')
jest.mock('@/lib/ai/fallback-manager')

describe('Chat Integration Workflow', () => {
  let mockSupabase: any
  let mockDbService: any
  let mockMemoryService: any
  let mockFallbackManager: any

  beforeEach(() => {
    // Setup mocks
    mockSupabase = {
      auth: {
        getSession: jest.fn().mockResolvedValue({
          data: { session: { user: { id: 'user123' } } }
        })
      }
    }

    mockDbService = {
      document: {
        findFirst: jest.fn().mockResolvedValue({
          id: 'doc123',
          status: 'READY',
          totalChunks: 5
        })
      },
      chunk: {
        findUnique: jest.fn().mockResolvedValue({
          id: 'chunk123',
          content: 'Test chunk content about photosynthesis'
        })
      },
      progress: {
        findUnique: jest.fn().mockResolvedValue({
          sessionId: 'session123',
          currentChunk: 0
        })
      }
    }

    mockMemoryService = {
      buildEducationalContext: jest.fn().mockResolvedValue('Educational context'),
      addConversationMessage: jest.fn().mockResolvedValue(undefined)
    }

    mockFallbackManager = {
      getCurrentProviderName: jest.fn().mockReturnValue('openai'),
      generateStreamWithFallback: jest.fn().mockImplementation(async function* () {
        yield 'Hello '
        yield 'student! '
        yield 'Let me help you understand photosynthesis.'
      })
    }

    // Apply mocks
    require('@/lib/supabase/server').createClient = jest.fn().mockReturnValue(mockSupabase)
    require('@/lib/db-service').dbService = mockDbService
    require('@/lib/ai/memory-service').memoryService = mockMemoryService
    require('@/lib/ai/fallback-manager').fallbackManager = mockFallbackManager

    jest.clearAllMocks()
  })

  describe('Streaming Chat API', () => {
    it('should handle complete streaming chat workflow', async () => {
      // Import the route handler
      const { POST } = require('../../../app/api/chat/stream/route')

      // Create mock request
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          chunkIndex: 0,
          message: 'What is photosynthesis?'
        })
      } as unknown as NextRequest

      // Execute the request
      const response = await POST(mockRequest)

      // Verify response is a streaming response
      expect(response).toBeInstanceOf(Response)
      expect(response.headers.get('Content-Type')).toBe('text/event-stream')

      // Verify all services were called correctly
      expect(mockSupabase.auth.getSession).toHaveBeenCalled()
      expect(mockDbService.document.findFirst).toHaveBeenCalledWith({
        where: { id: 'doc123', userId: 'user123' }
      })
      expect(mockDbService.chunk.findUnique).toHaveBeenCalled()
      expect(mockMemoryService.buildEducationalContext).toHaveBeenCalled()
      expect(mockFallbackManager.generateStreamWithFallback).toHaveBeenCalled()
    })

    it('should handle authentication errors', async () => {
      mockSupabase.auth.getSession.mockResolvedValue({ data: { session: null } })

      const { POST } = require('../../../app/api/chat/stream/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          chunkIndex: 0,
          message: 'Test message'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(401)
      expect(data.error).toBe('Unauthorized')
    })

    it('should handle document not found errors', async () => {
      mockDbService.document.findFirst.mockResolvedValue(null)

      const { POST } = require('../../../app/api/chat/stream/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'nonexistent',
          chunkIndex: 0,
          message: 'Test message'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(404)
      expect(data.error).toBe('Document not found or access denied')
    })

    it('should handle missing session ID errors', async () => {
      mockDbService.progress.findUnique.mockResolvedValue({ sessionId: null })

      const { POST } = require('../../../app/api/chat/stream/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          chunkIndex: 0,
          message: 'Test message'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(400)
      expect(data.error).toContain('AI session not initialized')
    })
  })

  describe('Chat History and Restoration', () => {
    it('should restore chat session successfully', async () => {
      mockMemoryService.getSessionInfo = jest.fn().mockResolvedValue({
        sessionId: 'session123',
        exists: true
      })
      mockMemoryService.getAllMemories = jest.fn().mockResolvedValue([
        {
          id: '1',
          metadata: { category: 'conversation', role: 'user' },
          memory: 'What is photosynthesis?',
          created_at: '2023-01-01T10:00:00Z'
        },
        {
          id: '2',
          metadata: { category: 'conversation', role: 'assistant' },
          memory: 'Photosynthesis is the process...',
          created_at: '2023-01-01T10:01:00Z'
        }
      ])

      const { POST } = require('../../../app/api/chat/restore/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          includeFullHistory: true
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.restored).toBe(true)
      expect(data.messages).toHaveLength(2)
      expect(data.sessionId).toBe('session123')
    })

    it('should handle no previous session', async () => {
      mockMemoryService.getSessionInfo = jest.fn().mockResolvedValue({
        sessionId: null,
        exists: false
      })

      const { POST } = require('../../../app/api/chat/restore/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.restored).toBe(false)
      expect(data.canInitialize).toBe(true)
    })
  })

  describe('AI Initialization', () => {
    it('should initialize AI for document successfully', async () => {
      const mockDocumentAIInitializer = {
        initializeAIForDocument: jest.fn().mockResolvedValue({
          success: true,
          sessionId: 'session123',
          initialResponse: 'Welcome! Let\'s explore this document together.'
        })
      }

      jest.doMock('@/lib/ai/document-ai-initializer', () => ({
        DocumentAIInitializer: jest.fn().mockImplementation(() => mockDocumentAIInitializer)
      }))

      const { POST } = require('../../../app/api/ai/initialize/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.success).toBe(true)
      expect(data.sessionId).toBe('session123')
    })
  })

  describe('Error Handling and Fallback', () => {
    it('should handle AI provider failures with fallback', async () => {
      // Mock primary provider failure, then fallback success
      mockFallbackManager.generateStreamWithFallback = jest.fn()
        .mockRejectedValueOnce(new Error('OpenAI API failed'))
        .mockImplementationOnce(async function* () {
          yield 'Fallback response from Anthropic'
        })

      const { POST } = require('../../../app/api/chat/stream/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          chunkIndex: 0,
          message: 'Test message'
        })
      } as unknown as NextRequest

      const response = await POST(mockRequest)

      expect(response).toBeInstanceOf(Response)
      expect(mockFallbackManager.generateStreamWithFallback).toHaveBeenCalled()
    })

    it('should handle memory service failures gracefully', async () => {
      mockMemoryService.buildEducationalContext.mockRejectedValue(new Error('Memory service failed'))
      mockMemoryService.addConversationMessage.mockRejectedValue(new Error('Memory service failed'))

      const { POST } = require('../../../app/api/chat/stream/route')
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          documentId: 'doc123',
          chunkIndex: 0,
          message: 'Test message'
        })
      } as unknown as NextRequest

      // Should not throw, should handle gracefully
      const response = await POST(mockRequest)
      expect(response).toBeInstanceOf(Response)
    })
  })

  describe('System Status', () => {
    it('should return system status', async () => {
      mockFallbackManager.getStatus = jest.fn().mockReturnValue({
        currentProvider: 'openai',
        availableProviders: ['openai', 'anthropic'],
        failedProviders: [],
        hasFallback: true
      })

      mockMemoryService.isAvailable = jest.fn().mockReturnValue(true)
      mockMemoryService.testConnection = jest.fn().mockResolvedValue(true)

      const { GET } = require('../../../app/api/system/status/route')
      const mockRequest = {} as NextRequest

      const response = await GET(mockRequest)
      const data = await response.json()

      expect(response.status).toBe(200)
      expect(data.overall).toBe('healthy')
      expect(data.services.ai.status).toBe('healthy')
      expect(data.services.memory.status).toBe('healthy')
    })
  })
})

describe('Performance and Caching', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  describe('Response Cache', () => {
    it('should cache and retrieve responses', async () => {
      const { responseCache } = require('../response-cache')
      
      // Cache a response
      responseCache.set('What is photosynthesis?', 'Photosynthesis is...', 'doc123', 0)
      
      // Retrieve cached response
      const cached = responseCache.get('What is photosynthesis?', 'doc123', 0)
      
      expect(cached).toBe('Photosynthesis is...')
    })

    it('should handle cache misses', async () => {
      const { responseCache } = require('../response-cache')
      
      const cached = responseCache.get('Uncached question', 'doc123', 0)
      
      expect(cached).toBeNull()
    })
  })

  describe('Connection Pool', () => {
    it('should manage provider connections', async () => {
      const { connectionPool } = require('../connection-pool')
      
      const stats = connectionPool.getStats()
      
      expect(stats).toEqual(expect.objectContaining({
        openai: expect.any(Object),
        anthropic: expect.any(Object)
      }))
    })
  })

  describe('Stream Buffer', () => {
    it('should buffer streaming tokens', async () => {
      const { StreamBuffer } = require('../stream-buffer')
      
      const flushedTokens: any[] = []
      const buffer = new StreamBuffer((tokens: any[]) => {
        flushedTokens.push(...tokens)
      })

      buffer.start()
      buffer.addToken('Hello')
      buffer.addToken(' ')
      buffer.addToken('world')
      
      await buffer.forceFlush()
      
      expect(flushedTokens).toHaveLength(3)
      expect(flushedTokens.map(t => t.content)).toEqual(['Hello', ' ', 'world'])
    })
  })
})