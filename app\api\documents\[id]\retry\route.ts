import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler"

export async function POST(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: documentId } = await params

    if (!documentId) {
      return NextResponse.json({ error: "Document ID is required" }, { status: 400 })
    }

    // Attempt to retry document processing
    const success = await ErrorHandler.retryDocumentProcessing(documentId, session.user.id)

    if (success) {
      return NextResponse.json({
        success: true,
        message: "Document processing retry initiated successfully",
        documentId
      })
    } else {
      return NextResponse.json({
        error: "Failed to retry document processing",
        documentId
      }, { status: 500 })
    }

  } catch (error) {
    console.error("Retry processing error:", error)

    const processedError = ErrorHandler.categorizeError(error)

    // Map error codes to appropriate HTTP status codes
    const statusCode = processedError.code === 'RATE_LIMIT_ERROR' ? 429 :
      processedError.code === 'API_KEY_ERROR' ? 401 :
        processedError.code === 'UNSUPPORTED_FORMAT' ? 400 :
          processedError.code === 'FILE_TOO_LARGE' ? 413 :
            processedError.code === 'TIMEOUT_ERROR' ? 408 :
              processedError.code === 'NETWORK_ERROR' ? 502 :
                processedError.code === 'DATABASE_ERROR' ? 503 :
                  processedError.code === 'STORAGE_ERROR' ? 502 :
                    500

    return NextResponse.json({
      error: processedError.userMessage,
      details: processedError.message,
      retryable: processedError.retryable,
      code: processedError.code
    }, { status: statusCode })
  }
}