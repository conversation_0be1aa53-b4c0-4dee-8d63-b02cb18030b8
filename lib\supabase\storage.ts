import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const createStorageClient = () => {
  return createClient(supabaseUrl, supabaseAnonKey)
}

export const uploadFileToSupabase = async (fileName: string, file: File, userId?: string, supabaseClient?: any) => {
  try {
    // Use the authenticated client if provided, otherwise fall back to storage client
    const supabase = supabaseClient || createStorageClient()
    const bucketName = 'documents'

    // Bucket 'documents' should already exist (created manually in Supabase Dashboard)

    // Create user-specific path for the file (required for RLS policies)
    const filePath = userId ? `${userId}/${fileName}` : fileName

    // Upload the file to Supabase storage
    const { data, error } = await supabase.storage
      .from(bucketName)
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: false,
      })

    if (error) throw error

    return { data, error: null }
  } catch (error) {
    console.error("Upload error:", error)
    return { data: null, error }
  }
}

export const getPublicUrl = (filePath: string) => {
  const supabase = createStorageClient()
  return supabase.storage.from('documents').getPublicUrl(filePath)
}
