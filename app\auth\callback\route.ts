import { type NextRequest, NextResponse } from "next/server"
import { prisma } from "@/lib/prisma"
import { createClient } from "@/lib/supabase/server"

export async function GET(request: NextRequest) {
  const requestUrl = new URL(request.url)
  const code = requestUrl.searchParams.get("code")
  const type = requestUrl.searchParams.get("type")

  if (code) {
    const supabase = await createClient()

    try {
      // Exchange the code for a session
      const { data: { session }, error: sessionError } = await supabase.auth.exchangeCodeForSession(code)

      if (sessionError) {
        console.error("Session error:", sessionError)
        return NextResponse.redirect(new URL("/auth?error=session_error", request.url))
      }

      if (session?.user) {
        const { user } = session

        // Check if user already exists in our database
        const existingUser = await prisma.user.findUnique({
          where: { email: user.email! },
          include: { profile: true }
        })

        if (!existingUser) {
          // Create new user and profile
          const newUser = await prisma.user.create({
            data: {
              id: user.id,
              email: user.email!,
              profile: {
                create: {
                  fullName: user.user_metadata?.full_name || "",
                  school: user.user_metadata?.school || "",
                  avatarUrl: user.user_metadata?.avatar_url || null,
                }
              }
            },
            include: {
              profile: true
            }
          })

          console.log("Created new user:", newUser.email)
        } else {
          console.log("User already exists:", existingUser.email)
        }
      }
    } catch (error) {
      console.error("Database error:", error)
      // Continue to redirect even if database operation fails
    }
  }

  // Handle different auth types and redirects
  if (code) {
    // User clicked email verification link - redirect to login page with success message
    return NextResponse.redirect(new URL("/auth?verified=true", request.url))
  } else if (type === 'signup') {
    // User just signed up but no verification code - show verify email page
    return NextResponse.redirect(new URL('/auth/verify-email', request.url))
  } else {
    // No code and not signup - redirect to auth page
    return NextResponse.redirect(new URL("/auth", request.url))
  }
}
