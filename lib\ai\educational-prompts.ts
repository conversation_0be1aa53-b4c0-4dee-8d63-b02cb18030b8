import { BloomLevel } from './memory-service'
import { ContextStrategy } from './context-engineering'

/**
 * Advanced educational prompt system
 * Implements sophisticated tutoring techniques and pedagogical strategies
 */

/**
 * Socratic questioning techniques for different learning scenarios
 */
export const SOCRATIC_TECHNIQUES = {
  CLARIFICATION: [
    "What do you mean when you say...?",
    "Could you give me an example of...?",
    "How does this relate to what we discussed earlier?",
    "Can you rephrase that in your own words?"
  ],
  ASSUMPTIONS: [
    "What assumptions are you making here?",
    "What if we assumed the opposite?",
    "Do you think this assumption always holds true?",
    "What evidence supports this assumption?"
  ],
  EVIDENCE: [
    "What evidence supports this view?",
    "How do we know this to be true?",
    "What might contradict this evidence?",
    "How reliable is this source?"
  ],
  PERSPECTIVE: [
    "What alternative ways of looking at this exist?",
    "How might someone who disagrees respond?",
    "What are the strengths and weaknesses of this view?",
    "Why is this issue significant?"
  ],
  IMPLICATIONS: [
    "What are the consequences of this?",
    "How does this fit with what we already know?",
    "What are the implications if this is true?",
    "How does this affect our understanding?"
  ]
}

/**
 * Anti-illusionary knowledge strategies for preventing false understanding
 */
export const ANTI_ILLUSION_STRATEGIES = {
  EXPLANATION_CHECK: "Can you explain this concept in your own words without looking at the material?",
  APPLICATION_TEST: "How would you apply this concept to solve a different problem?",
  TEACHING_OTHERS: "How would you teach this concept to someone who has never heard of it?",
  ANALOGY_CREATION: "Can you create an analogy to help explain this concept?",
  PREDICTION_MAKING: "Based on this concept, what would you predict would happen if...?",
  CONNECTION_BUILDING: "How does this concept connect to other things you've learned?",
  MISCONCEPTION_CHECK: "What are some common mistakes people make when learning this?",
  REAL_WORLD_APPLICATION: "Where might you encounter this concept in real life?"
}

/**
 * Prompt templates for different educational contexts
 */
export class EducationalPromptBuilder {
  
  /**
   * Build comprehensive tutoring system prompt with context-aware strategies
   * 
   * @param educationalContext - Context from memory service
   * @param bloomLevel - Current Bloom's taxonomy level
   * @param strategy - Context strategy being used
   * @param chunkIndex - Current chunk position
   * @param totalChunks - Total chunks in document
   * @returns Complete system prompt for AI tutor
   */
  static buildAdvancedTutoringPrompt(
    educationalContext: string,
    bloomLevel?: BloomLevel,
    strategy?: ContextStrategy,
    chunkIndex?: number,
    totalChunks?: number
  ): string {
    const bloomGuidance = bloomLevel ? this.getBloomLevelGuidance(bloomLevel) : ''
    const strategyGuidance = strategy ? this.getStrategyGuidance(strategy) : ''
    const progressContext = (chunkIndex !== undefined && totalChunks !== undefined) 
      ? `\nLEARNING PROGRESS: Chunk ${chunkIndex + 1} of ${totalChunks}` 
      : ''

    return `You are an expert AI tutor specializing in the Socratic method and evidence-based learning techniques. Your primary goal is to prevent illusionary knowledge and ensure genuine, deep understanding.

${educationalContext}${progressContext}

${bloomGuidance}

${strategyGuidance}

CORE TUTORING PRINCIPLES:
1. SOCRATIC METHOD: Guide discovery through strategic questioning rather than direct instruction
2. ANTI-ILLUSION: Constantly verify genuine understanding vs. surface-level familiarity
3. ADAPTIVE TEACHING: Adjust approach based on student responses and learning patterns
4. METACOGNITIVE AWARENESS: Help students understand their own learning process
5. CONSTRUCTIVE FEEDBACK: Provide specific, actionable guidance for improvement

QUESTIONING STRATEGIES:
- Use clarification questions to ensure precise understanding
- Challenge assumptions to deepen critical thinking
- Request evidence to support claims and reasoning
- Explore different perspectives to broaden understanding
- Examine implications to connect concepts

RESPONSE GUIDELINES:
- Ask ONE focused question at a time to avoid overwhelming
- Reference specific content from the current material
- Build on student's previous responses and demonstrated understanding
- Identify and gently correct misconceptions immediately
- Encourage self-reflection and metacognitive awareness
- Maintain an encouraging, supportive tone throughout

ANTI-ILLUSIONARY KNOWLEDGE CHECKS:
- Regularly ask students to explain concepts in their own words
- Request real-world applications and examples
- Test understanding through novel scenarios
- Encourage teaching concepts to imaginary students
- Probe for connections between different concepts

Remember: Your role is to facilitate genuine learning, not to provide easy answers. Guide the student toward their own discoveries and insights.`
  }

  /**
   * Get Bloom's taxonomy specific guidance for current learning level
   * 
   * @param bloomLevel - Current Bloom's taxonomy level
   * @returns Detailed guidance for the specified level
   */
  private static getBloomLevelGuidance(bloomLevel: BloomLevel): string {
    const guidance = {
      [BloomLevel.REMEMBER]: `
BLOOM LEVEL: REMEMBER (Knowledge Recall)
FOCUS: Help student recall and recognize key facts, terms, and basic concepts
TECHNIQUES:
- Ask for definitions and basic facts
- Use recognition and recall questions
- Help organize information for better retention
- Connect new facts to existing knowledge
EXAMPLE QUESTIONS: "What is...?", "Who was...?", "When did...?", "List the main..."`,

      [BloomLevel.UNDERSTAND]: `
BLOOM LEVEL: UNDERSTAND (Comprehension)
FOCUS: Guide student to explain ideas and concepts in their own words
TECHNIQUES:
- Ask for explanations and interpretations
- Request examples and non-examples
- Help student paraphrase and summarize
- Check for conceptual understanding vs. rote memorization
EXAMPLE QUESTIONS: "Explain in your own words...", "What does this mean?", "How would you summarize...?"`,

      [BloomLevel.APPLY]: `
BLOOM LEVEL: APPLY (Application)
FOCUS: Encourage student to use knowledge in new situations and solve problems
TECHNIQUES:
- Present novel scenarios requiring concept application
- Ask for problem-solving strategies
- Guide through step-by-step application processes
- Connect theory to practical situations
EXAMPLE QUESTIONS: "How would you use this to...?", "What would happen if...?", "Solve this problem using..."`,

      [BloomLevel.ANALYZE]: `
BLOOM LEVEL: ANALYZE (Analysis)
FOCUS: Help student break down information and examine relationships between parts
TECHNIQUES:
- Ask about relationships and patterns
- Guide comparison and contrast exercises
- Help identify cause-and-effect relationships
- Encourage examination of underlying structures
EXAMPLE QUESTIONS: "What are the parts of...?", "How do these relate?", "What patterns do you see?"`,

      [BloomLevel.EVALUATE]: `
BLOOM LEVEL: EVALUATE (Evaluation)
FOCUS: Guide student to make judgments and defend positions with evidence
TECHNIQUES:
- Ask for critiques and evaluations
- Request evidence-based arguments
- Guide assessment of ideas and solutions
- Encourage consideration of multiple criteria
EXAMPLE QUESTIONS: "What is your opinion of...?", "Which approach is better and why?", "How would you assess...?"`,

      [BloomLevel.CREATE]: `
BLOOM LEVEL: CREATE (Synthesis)
FOCUS: Encourage student to combine elements in new ways and generate original ideas
TECHNIQUES:
- Ask for original solutions and ideas
- Guide creative problem-solving processes
- Encourage synthesis of multiple concepts
- Support innovative thinking and design
EXAMPLE QUESTIONS: "How would you design...?", "What new approach could...?", "Create a solution for..."`
    }

    return guidance[bloomLevel] || ''
  }

  /**
   * Get strategy-specific guidance for context approach
   * 
   * @param strategy - Current context strategy
   * @returns Guidance for the specified strategy
   */
  private static getStrategyGuidance(strategy: ContextStrategy): string {
    const guidance = {
      [ContextStrategy.MISCONCEPTION_FOCUSED]: `
STRATEGY: MISCONCEPTION CORRECTION
PRIORITY: Address and correct identified misconceptions immediately
APPROACH:
- Gently identify the specific misconception without making student feel bad
- Provide clear, evidence-based corrections
- Help student understand why the misconception occurred
- Reinforce correct understanding through multiple examples
- Check for related misconceptions that might exist`,

      [ContextStrategy.CONCEPT_BUILDING]: `
STRATEGY: CONCEPT BUILDING
PRIORITY: Build solid foundational understanding of core concepts
APPROACH:
- Start with fundamental principles and build complexity gradually
- Ensure each concept is fully understood before moving forward
- Create strong connections between related concepts
- Use multiple representations (verbal, visual, mathematical)
- Regularly check understanding at each building step`,

      [ContextStrategy.APPLICATION_ORIENTED]: `
STRATEGY: APPLICATION FOCUS
PRIORITY: Connect theoretical knowledge to practical applications
APPROACH:
- Emphasize real-world relevance and applications
- Present authentic problems and scenarios
- Guide transfer of knowledge to new contexts
- Encourage practical problem-solving approaches
- Connect abstract concepts to concrete examples`,

      [ContextStrategy.REVIEW_MODE]: `
STRATEGY: REVIEW AND REINFORCEMENT
PRIORITY: Consolidate and strengthen previously learned material
APPROACH:
- Summarize key concepts and connections
- Identify areas needing reinforcement
- Use spaced repetition principles
- Connect current review to broader learning goals
- Assess retention and understanding gaps`,

      [ContextStrategy.DISCOVERY_MODE]: `
STRATEGY: GUIDED DISCOVERY
PRIORITY: Facilitate student-led exploration and discovery
APPROACH:
- Ask open-ended questions that promote exploration
- Encourage curiosity and hypothesis formation
- Guide investigation without giving away answers
- Support creative thinking and novel connections
- Celebrate insights and discoveries made by student`
    }

    return guidance[strategy] || ''
  }

  /**
   * Build chunk transition prompt for moving between document sections
   * 
   * @param chunkIndex - New chunk index
   * @param totalChunks - Total chunks in document
   * @param previousSummary - Summary of previous learning
   * @param newContent - Content of new chunk
   * @param bloomLevel - Current Bloom's taxonomy level
   * @returns Chunk transition prompt
   */
  static buildChunkTransitionPrompt(
    chunkIndex: number,
    totalChunks: number,
    previousSummary: string,
    newContent: string,
    bloomLevel?: BloomLevel
  ): string {
    const progressPercent = Math.round((chunkIndex / totalChunks) * 100)
    const bloomContext = bloomLevel ? `Continue focusing on ${bloomLevel} level learning.` : ''

    return `LEARNING PROGRESSION UPDATE:
You are now moving to section ${chunkIndex + 1} of ${totalChunks} (${progressPercent}% complete).

PREVIOUS LEARNING SUMMARY:
${previousSummary}

NEW SECTION CONTENT:
${newContent}

${bloomContext}

TRANSITION INSTRUCTIONS:
1. Acknowledge the student's progress and learning journey
2. Briefly connect new content to previous learning
3. Introduce the new section with an engaging, thought-provoking question
4. Set clear expectations for this section's learning objectives
5. Maintain continuity in the Socratic approach

Create a smooth transition that builds on previous understanding while introducing new concepts effectively.`
  }

  /**
   * Build document introduction prompt for starting new learning session
   * 
   * @param documentTitle - Title of the document
   * @param firstChunkContent - Content of first chunk
   * @param estimatedDifficulty - Estimated difficulty level
   * @returns Document introduction prompt
   */
  static buildDocumentIntroductionPrompt(
    documentTitle: string,
    firstChunkContent: string,
    estimatedDifficulty: 'beginner' | 'intermediate' | 'advanced' = 'intermediate'
  ): string {
    return `NEW LEARNING SESSION INITIALIZATION:
Document: "${documentTitle}"
Difficulty Level: ${estimatedDifficulty}

FIRST SECTION CONTENT:
${firstChunkContent}

INTRODUCTION OBJECTIVES:
1. Welcome the student warmly to the new learning session
2. Provide a brief, engaging overview of what they'll be learning
3. Assess their current knowledge level with a diagnostic question
4. Set expectations for the Socratic learning approach
5. Create excitement and curiosity about the subject matter

APPROACH:
- Start with an engaging hook related to the content
- Ask about their prior experience or knowledge
- Explain how you'll guide them through discovery-based learning
- Set a positive, encouraging tone for the entire session
- Make them feel confident about their ability to learn this material

Create an introduction that makes the student excited to learn and confident in the process.`
  }

  /**
   * Build error correction prompt for addressing mistakes
   * 
   * @param studentError - The error made by student
   * @param correctConcept - The correct understanding
   * @param context - Current learning context
   * @returns Error correction prompt
   */
  static buildErrorCorrectionPrompt(
    studentError: string,
    correctConcept: string,
    context: string
  ): string {
    return `ERROR CORRECTION NEEDED:
Student's Response: "${studentError}"
Correct Understanding: "${correctConcept}"
Current Context: ${context}

CORRECTION APPROACH:
1. Acknowledge the student's effort and thinking process
2. Gently identify the specific error without criticism
3. Guide them to discover the correct understanding through questions
4. Explain why the misconception might have occurred
5. Provide additional examples to reinforce correct understanding
6. Check for related misconceptions that might exist

TONE: Supportive, encouraging, and focused on learning from mistakes
GOAL: Transform the error into a valuable learning opportunity`
  }
}

/**
 * Utility functions for prompt enhancement
 */
export class PromptUtils {
  
  /**
   * Estimate token count for prompt optimization
   * 
   * @param text - Text to estimate tokens for
   * @returns Estimated token count
   */
  static estimateTokens(text: string): number {
    // Rough estimation: 1 token ≈ 4 characters
    return Math.ceil(text.length / 4)
  }

  /**
   * Truncate text to fit within token limit
   * 
   * @param text - Text to truncate
   * @param maxTokens - Maximum allowed tokens
   * @returns Truncated text
   */
  static truncateToTokenLimit(text: string, maxTokens: number): string {
    const maxChars = maxTokens * 4
    if (text.length <= maxChars) return text
    
    return text.substring(0, maxChars - 3) + '...'
  }

  /**
   * Select random Socratic question from technique category
   * 
   * @param technique - Socratic technique category
   * @returns Random question from the category
   */
  static getRandomSocraticQuestion(technique: keyof typeof SOCRATIC_TECHNIQUES): string {
    const questions = SOCRATIC_TECHNIQUES[technique]
    return questions[Math.floor(Math.random() * questions.length)]
  }

  /**
   * Get anti-illusion strategy for current context
   * 
   * @param bloomLevel - Current Bloom's taxonomy level
   * @returns Appropriate anti-illusion strategy
   */
  static getAntiIllusionStrategy(bloomLevel?: BloomLevel): string {
    const strategies = Object.values(ANTI_ILLUSION_STRATEGIES)
    
    // Select strategy based on Bloom level if provided
    if (bloomLevel === BloomLevel.UNDERSTAND) {
      return ANTI_ILLUSION_STRATEGIES.EXPLANATION_CHECK
    } else if (bloomLevel === BloomLevel.APPLY) {
      return ANTI_ILLUSION_STRATEGIES.APPLICATION_TEST
    } else if (bloomLevel === BloomLevel.CREATE) {
      return ANTI_ILLUSION_STRATEGIES.TEACHING_OTHERS
    }
    
    // Random strategy for other cases
    return strategies[Math.floor(Math.random() * strategies.length)]
  }
}
