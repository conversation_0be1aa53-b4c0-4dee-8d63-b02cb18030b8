import { promises as fs } from 'fs'
import { join } from 'path'
import { tmpdir } from 'os'
import { randomUUID } from 'crypto'

export interface TempFileInfo {
  filePath: string
  originalName: string
  size: number
  createdAt: number
}

export class TempFileService {
  private static readonly TEMP_DIR = join(tmpdir(), 'document-processing')
  private static readonly MAX_AGE = 1000 * 60 * 60 // 1 hour
  private static fileRegistry = new Map<string, TempFileInfo>()

  /**
   * Initialize temp directory
   */
  static async init(): Promise<void> {
    try {
      await fs.mkdir(this.TEMP_DIR, { recursive: true })
      console.log(`[TempFile] Temp directory initialized: ${this.TEMP_DIR}`)
    } catch (error) {
      console.error('[TempFile] Failed to initialize temp directory:', error)
      throw error
    }
  }

  /**
   * Store buffer as temporary file and return reference
   */
  static async storeBuffer(buffer: Buffer, originalName: string): Promise<string> {
    await this.init()
    
    const fileId = randomUUID()
    const fileName = `${fileId}_${originalName.replace(/[^a-zA-Z0-9.-]/g, '_')}`
    const filePath = join(this.TEMP_DIR, fileName)

    try {
      await fs.writeFile(filePath, buffer)
      
      const fileInfo: TempFileInfo = {
        filePath,
        originalName,
        size: buffer.length,
        createdAt: Date.now()
      }
      
      this.fileRegistry.set(fileId, fileInfo)
      
      console.log(`[TempFile] Stored ${originalName} (${buffer.length} bytes) as ${fileId}`)
      return fileId
    } catch (error) {
      console.error(`[TempFile] Failed to store file ${originalName}:`, error)
      throw error
    }
  }

  /**
   * Retrieve buffer from temporary file
   */
  static async getBuffer(fileId: string): Promise<Buffer | null> {
    const fileInfo = this.fileRegistry.get(fileId)
    if (!fileInfo) {
      console.warn(`[TempFile] File ID ${fileId} not found in registry`)
      return null
    }

    try {
      const buffer = await fs.readFile(fileInfo.filePath)
      console.log(`[TempFile] Retrieved ${fileInfo.originalName} (${buffer.length} bytes)`)
      return buffer
    } catch (error) {
      console.error(`[TempFile] Failed to read file ${fileId}:`, error)
      // Clean up registry entry if file doesn't exist
      this.fileRegistry.delete(fileId)
      return null
    }
  }

  /**
   * Get file info without reading the buffer
   */
  static getFileInfo(fileId: string): TempFileInfo | null {
    return this.fileRegistry.get(fileId) || null
  }

  /**
   * Delete temporary file and clean up
   */
  static async deleteFile(fileId: string): Promise<boolean> {
    const fileInfo = this.fileRegistry.get(fileId)
    if (!fileInfo) {
      return false
    }

    try {
      await fs.unlink(fileInfo.filePath)
      this.fileRegistry.delete(fileId)
      console.log(`[TempFile] Deleted ${fileInfo.originalName} (${fileId})`)
      return true
    } catch (error) {
      console.error(`[TempFile] Failed to delete file ${fileId}:`, error)
      // Still remove from registry even if file deletion failed
      this.fileRegistry.delete(fileId)
      return false
    }
  }

  /**
   * Clean up expired temporary files
   */
  static async cleanup(): Promise<void> {
    const now = Date.now()
    const expiredFiles: string[] = []

    // Find expired files
    for (const [fileId, fileInfo] of this.fileRegistry.entries()) {
      if (now - fileInfo.createdAt > this.MAX_AGE) {
        expiredFiles.push(fileId)
      }
    }

    // Delete expired files
    let deletedCount = 0
    for (const fileId of expiredFiles) {
      if (await this.deleteFile(fileId)) {
        deletedCount++
      }
    }

    if (deletedCount > 0) {
      console.log(`[TempFile] Cleanup completed: deleted ${deletedCount} expired files`)
    }
  }

  /**
   * Get storage statistics
   */
  static getStats() {
    const files = Array.from(this.fileRegistry.values())
    const totalSize = files.reduce((sum, file) => sum + file.size, 0)
    
    return {
      fileCount: files.length,
      totalSize,
      averageSize: files.length > 0 ? Math.round(totalSize / files.length) : 0,
      oldestFile: files.length > 0 ? Math.min(...files.map(f => f.createdAt)) : null
    }
  }

  /**
   * Emergency cleanup - delete all temporary files
   */
  static async emergencyCleanup(): Promise<void> {
    console.log('[TempFile] Starting emergency cleanup...')
    
    const fileIds = Array.from(this.fileRegistry.keys())
    let deletedCount = 0
    
    for (const fileId of fileIds) {
      if (await this.deleteFile(fileId)) {
        deletedCount++
      }
    }
    
    console.log(`[TempFile] Emergency cleanup completed: deleted ${deletedCount} files`)
  }
}

// Initialize and start cleanup interval
TempFileService.init().catch(console.error)

// Cleanup every 15 minutes
setInterval(() => {
  TempFileService.cleanup().catch(console.error)
}, 15 * 60 * 1000)

// Emergency cleanup on process exit
process.on('SIGINT', () => {
  TempFileService.emergencyCleanup().finally(() => process.exit(0))
})

process.on('SIGTERM', () => {
  TempFileService.emergencyCleanup().finally(() => process.exit(0))
})
