/**
 * Prompt Effectiveness and AI Response Quality Tests
 * Tests to validate prompt effectiveness and AI response quality
 */

// Jest globals are available globally, no need to import
import { buildAdvancedTutoringSystemPrompt, getBloomLevelGuidance } from '../prompts'
import { BloomLevel } from '../memory-service'

describe('Prompt Effectiveness', () => {
  describe('Educational Prompts', () => {
    it('should build comprehensive tutoring system prompt', () => {
      const educationalContext = `
        CURRENT CONTENT: Photosynthesis is the process by which plants convert sunlight into energy.
        RELEVANT LEARNING CONTEXT: Student previously asked about plant nutrition.
        STUDENT QUESTION: How do plants make their own food?
      `

      const prompt = buildAdvancedTutoringSystemPrompt(
        educationalContext,
        BloomLevel.UNDERSTAND,
        'socratic',
        1,
        5
      )

      expect(prompt).toContain('Socratic method')
      expect(prompt).toContain('expert AI tutor')
      expect(prompt).toContain('UNDERSTAND')
      expect(prompt).toContain('Chunk 2 of 5')
      expect(prompt).toContain(educationalContext)
    })

    it('should adapt prompts for different Bloom levels', () => {
      const contexts = [
        { level: BloomLevel.REMEMBER, expectedKeywords: ['recall', 'identify', 'list'] },
        { level: BloomLevel.UNDERSTAND, expectedKeywords: ['explain', 'describe', 'interpret'] },
        { level: BloomLevel.APPLY, expectedKeywords: ['apply', 'demonstrate', 'use'] },
        { level: BloomLevel.ANALYZE, expectedKeywords: ['analyze', 'compare', 'examine'] },
        { level: BloomLevel.EVALUATE, expectedKeywords: ['evaluate', 'judge', 'critique'] },
        { level: BloomLevel.CREATE, expectedKeywords: ['create', 'design', 'compose'] }
      ]

      contexts.forEach(({ level, expectedKeywords }) => {
        const guidance = getBloomLevelGuidance(level)
        
        expect(guidance).toBeDefined()
        expect(typeof guidance).toBe('string')
        
        // Check that guidance contains appropriate keywords for the level
        const hasRelevantKeywords = expectedKeywords.some(keyword => 
          guidance.toLowerCase().includes(keyword.toLowerCase())
        )
        expect(hasRelevantKeywords).toBe(true)
      })
    })

    it('should include memory context in prompts', () => {
      const educationalContext = `
        RELEVANT LEARNING CONTEXT:
        1. Student struggled with understanding cellular respiration
        2. Student correctly identified chloroplasts
        
        MISCONCEPTIONS TO ADDRESS:
        1. Student thinks plants don't breathe
      `

      const prompt = buildAdvancedTutoringSystemPrompt(
        educationalContext,
        BloomLevel.UNDERSTAND
      )

      expect(prompt).toContain('cellular respiration')
      expect(prompt).toContain('chloroplasts')
      expect(prompt).toContain('misconceptions')
    })

    it('should provide chunk navigation context', () => {
      const prompt = buildAdvancedTutoringSystemPrompt(
        'Test content',
        BloomLevel.UNDERSTAND,
        'socratic',
        3,
        10
      )

      expect(prompt).toContain('Chunk 4 of 10')
      expect(prompt).toContain('LEARNING PROGRESS')
    })
  })

  describe('Response Quality Validation', () => {
    let mockAIResponse: string

    beforeEach(() => {
      mockAIResponse = `
        Great question! Let me help you understand photosynthesis step by step.
        
        First, can you tell me what you already know about how plants get energy?
        
        Think about what plants need from their environment - what do you see plants reaching toward?
        
        This will help us build on your existing knowledge to understand this fascinating process.
      `
    })

    it('should validate educational response structure', () => {
      const validation = validateEducationalResponse(mockAIResponse)

      expect(validation.hasSocraticElements).toBe(true)
      expect(validation.hasQuestions).toBe(true)
      expect(validation.isEncouraging).toBe(true)
      expect(validation.buildsOnPriorKnowledge).toBe(true)
    })

    it('should detect inappropriate direct answers', () => {
      const directAnswer = `
        Photosynthesis is the process where plants use chlorophyll to convert 
        carbon dioxide and water into glucose using sunlight energy. 
        The equation is 6CO2 + 6H2O + light energy → C6H12O6 + 6O2.
      `

      const validation = validateEducationalResponse(directAnswer)

      expect(validation.hasSocraticElements).toBe(false)
      expect(validation.isDirectAnswer).toBe(true)
      expect(validation.hasQuestions).toBe(false)
    })

    it('should validate response appropriateness for Bloom level', () => {
      const responses = {
        [BloomLevel.REMEMBER]: 'Can you list the main components needed for photosynthesis?',
        [BloomLevel.UNDERSTAND]: 'How would you explain photosynthesis to a friend?',
        [BloomLevel.APPLY]: 'How might a plant in a dark room be affected?',
        [BloomLevel.ANALYZE]: 'What would happen if we removed one component of photosynthesis?',
        [BloomLevel.EVALUATE]: 'Which is more important for plant survival - photosynthesis or respiration?',
        [BloomLevel.CREATE]: 'Design an experiment to test photosynthesis efficiency.'
      }

      Object.entries(responses).forEach(([level, response]) => {
        const validation = validateResponseForBloomLevel(response, level as BloomLevel)
        expect(validation.appropriate).toBe(true)
        expect(validation.bloomLevel).toBe(level)
      })
    })

    it('should detect off-topic responses', () => {
      const offTopicResponse = `
        Let me tell you about my favorite recipe for chocolate cake.
        First, you need to preheat the oven to 350 degrees...
      `

      const validation = validateEducationalResponse(offTopicResponse, 'photosynthesis')

      expect(validation.isOnTopic).toBe(false)
      expect(validation.relevanceScore).toBeLessThan(0.3)
    })

    it('should validate response length appropriateness', () => {
      const tooShort = 'Yes.'
      const tooLong = 'A'.repeat(2000) // Very long response
      const appropriate = mockAIResponse

      expect(validateResponseLength(tooShort)).toBe(false)
      expect(validateResponseLength(tooLong)).toBe(false)
      expect(validateResponseLength(appropriate)).toBe(true)
    })

    it('should detect encouraging language', () => {
      const encouragingPhrases = [
        'Great question!',
        'Excellent thinking!',
        'You\'re on the right track!',
        'That\'s a thoughtful observation!',
        'Well done!'
      ]

      encouragingPhrases.forEach(phrase => {
        const validation = validateEducationalResponse(phrase)
        expect(validation.isEncouraging).toBe(true)
      })
    })
  })

  describe('Context Integration Quality', () => {
    it('should effectively use memory context', () => {
      const memoryContext = [
        'Student previously asked about plant cells',
        'Student correctly identified chloroplasts',
        'Student confused about cellular respiration'
      ]

      const response = generateMockResponseWithContext(memoryContext)
      const validation = validateContextIntegration(response, memoryContext)

      expect(validation.usesMemoryContext).toBe(true)
      expect(validation.buildsOnPreviousLearning).toBe(true)
      expect(validation.addressesMisconceptions).toBe(true)
    })

    it('should maintain conversation continuity', () => {
      const conversationHistory = [
        { role: 'user', content: 'What is photosynthesis?' },
        { role: 'assistant', content: 'Great question! What do you know about plants and sunlight?' },
        { role: 'user', content: 'Plants need sunlight to grow' }
      ]

      const nextResponse = 'Exactly! Now, what do you think plants do with that sunlight?'
      const validation = validateConversationContinuity(nextResponse, conversationHistory)

      expect(validation.maintainsContinuity).toBe(true)
      expect(validation.acknowledgesPreviousResponse).toBe(true)
      expect(validation.progressesLearning).toBe(true)
    })
  })
})

// Helper functions for validation
function validateEducationalResponse(response: string, topic?: string) {
  const lowerResponse = response.toLowerCase()
  
  return {
    hasSocraticElements: lowerResponse.includes('?') && 
                        (lowerResponse.includes('what') || lowerResponse.includes('how') || lowerResponse.includes('why')),
    hasQuestions: (response.match(/\?/g) || []).length > 0,
    isEncouraging: /great|excellent|good|well done|fantastic|wonderful|thoughtful|right track/i.test(response),
    buildsOnPriorKnowledge: /you know|you mentioned|remember|think about|consider/i.test(response),
    isDirectAnswer: /is the process|the answer is|simply put/i.test(response),
    isOnTopic: topic ? calculateTopicRelevance(response, topic) > 0.5 : true,
    relevanceScore: topic ? calculateTopicRelevance(response, topic) : 1.0
  }
}

function validateResponseForBloomLevel(response: string, bloomLevel: BloomLevel) {
  const bloomKeywords = {
    [BloomLevel.REMEMBER]: ['list', 'identify', 'name', 'recall', 'what', 'components', 'main'],
    [BloomLevel.UNDERSTAND]: ['explain', 'describe', 'how', 'why', 'interpret', 'would you explain'],
    [BloomLevel.APPLY]: ['use', 'apply', 'demonstrate', 'show', 'example', 'might', 'affected'],
    [BloomLevel.ANALYZE]: ['analyze', 'compare', 'examine', 'break down', 'relationship', 'what would happen', 'removed'],
    [BloomLevel.EVALUATE]: ['evaluate', 'judge', 'assess', 'critique', 'better', 'which', 'more important'],
    [BloomLevel.CREATE]: ['create', 'design', 'build', 'compose', 'develop', 'experiment']
  }

  const keywords = bloomKeywords[bloomLevel]
  const hasAppropriateKeywords = keywords.some(keyword => 
    response.toLowerCase().includes(keyword)
  )

  return {
    appropriate: hasAppropriateKeywords,
    bloomLevel,
    matchingKeywords: keywords.filter(keyword => 
      response.toLowerCase().includes(keyword)
    )
  }
}

function validateResponseLength(response: string) {
  const wordCount = response.trim().split(/\s+/).length
  return wordCount >= 10 && wordCount <= 200 // Reasonable range for educational responses
}

function calculateTopicRelevance(response: string, topic: string) {
  const responseWords = response.toLowerCase().split(/\s+/)
  const topicWords = topic.toLowerCase().split(/\s+/)
  
  const matches = responseWords.filter(word => 
    topicWords.some(topicWord => 
      word.includes(topicWord) || topicWord.includes(word)
    )
  )
  
  return matches.length / Math.max(responseWords.length, topicWords.length)
}

function generateMockResponseWithContext(memoryContext: string[]) {
  return `
    I remember you asked about plant cells earlier, and you correctly identified chloroplasts! 
    Now, building on that knowledge, let's explore how those chloroplasts are involved in photosynthesis.
    
    You mentioned some confusion about cellular respiration - that's completely normal! 
    Let's clarify the difference between these two important processes.
    
    What do you think happens inside those chloroplasts when sunlight hits them?
  `
}

function validateContextIntegration(response: string, memoryContext: string[]) {
  const lowerResponse = response.toLowerCase()
  
  return {
    usesMemoryContext: memoryContext.some(context => {
      const contextWords = context.toLowerCase().split(' ')
      return contextWords.some(word => 
        word.length > 3 && lowerResponse.includes(word)
      )
    }),
    buildsOnPreviousLearning: /remember|earlier|previously|you mentioned|building on/i.test(response),
    addressesMisconceptions: /confusion|clarify|difference|let's clear up/i.test(response)
  }
}

function validateConversationContinuity(response: string, conversationHistory: any[]) {
  const lastUserMessage = conversationHistory[conversationHistory.length - 1]?.content || ''
  const lowerResponse = response.toLowerCase()
  const lowerLastMessage = lastUserMessage.toLowerCase()
  
  return {
    maintainsContinuity: true, // Simplified for testing
    acknowledgesPreviousResponse: /exactly|yes|that's right|good|correct/i.test(response),
    progressesLearning: lowerResponse.includes('?') || /now|next|let's explore/i.test(response)
  }
}