import { type NextRequest, NextResponse } from "next/server"
import { createClientWithRetry, withAuthRetry } from "@/lib/supabase/client-with-retry"
import { dbService } from "@/lib/db-service"

export async function GET(_request: NextRequest) {
  try {
    // Use enhanced client with retry logic
    const supabase = await createClientWithRetry({ maxRetries: 2, baseDelay: 1000 })
    
    // Wrap auth operation with retry logic
    const { user, authError } = await withAuthRetry(async () => {
      const {
        data: { user },
        error: authError,
      } = await supabase.auth.getUser()
      
      return { user, authError }
    }, 'user authentication')

    if (authError || !user) {
      console.warn('Authentication failed:', authError)
      return NextResponse.json({ 
        error: "Authentication failed", 
        details: authError?.message || "User not authenticated",
        retryable: true
      }, { status: 401 })
    }

    // Add retry logic for database connection issues
    let documents
    let retryCount = 0
    const maxRetries = 3

    while (retryCount < maxRetries) {
      try {
        documents = await dbService.document.findMany({
          where: { userId: user.id },
          include: {
            progress: true,
          },
          orderBy: { createdAt: "desc" },
        })
        break // Success, exit retry loop
      } catch (dbError) {
        retryCount++
        console.warn(`Database query attempt ${retryCount} failed:`, dbError)
        
        if (retryCount >= maxRetries) {
          throw dbError // Re-throw after max retries
        }
        
        // Wait before retry (exponential backoff)
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 1000))
      }
    }

    const documentsWithProgress = documents.map((doc: any) => ({
      id: doc.id,
      filename: doc.fileName, // Changed to match component interface
      status: doc.status,
      errorMessage: doc.errorMessage,
      totalChunks: doc.totalChunks,
      createdAt: doc.createdAt,
      progress: doc.progress[0]
        ? {
            currentChunk: doc.progress[0].currentChunk,
            completed: doc.progress[0].currentChunk >= doc.totalChunks - 1
          }
        : undefined,
    }))

    return NextResponse.json({ documents: documentsWithProgress })
  } catch (error) {
    console.error("Fetch documents error:", error)
    
    // Provide more detailed error information
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    const isConnectionError = errorMessage.includes("connect") || errorMessage.includes("timeout")
    
    return NextResponse.json({ 
      error: "Internal server error",
      details: isConnectionError ? "Database connection issue" : "Query failed",
      retryable: isConnectionError
    }, { status: 500 })
  }
}
