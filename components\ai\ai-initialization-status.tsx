"use client"

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Loader2, Brain, CheckCircle, AlertCircle, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

/**
 * AI Initialization Status Component
 * Shows the status of AI initialization for documents and allows manual initialization
 */

interface AIInitializationStatusProps {
  documentId: string
  documentName: string
  onInitializationComplete?: (sessionId: string, initialResponse: string) => void
}

interface AIStatus {
  documentId: string
  documentName: string
  documentStatus: string
  isReady: boolean
  isInitialized: boolean
  totalChunks: number
  canInitialize: boolean
}

export function AIInitializationStatus({ 
  documentId, 
  documentName, 
  onInitializationComplete 
}: AIInitializationStatusProps) {
  const [status, setStatus] = useState<AIStatus | null>(null)
  const [loading, setLoading] = useState(true)
  const [initializing, setInitializing] = useState(false)
  const [error, setError] = useState<string | null>(null)

  /**
   * Fetch AI initialization status from API
   */
  const fetchStatus = async () => {
    try {
      setLoading(true)
      setError(null)

      const response = await fetch(
        `/api/ai/initialize?documentId=${encodeURIComponent(documentId)}`
      )

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to fetch AI status')
      }

      const statusData = await response.json()
      setStatus(statusData)
    } catch (err) {
      console.error('Failed to fetch AI status:', err)
      setError(err instanceof Error ? err.message : 'Unknown error')
    } finally {
      setLoading(false)
    }
  }
  /**
   * Initialize AI for the document
   */
  const initializeAI = async (forceReinitialize = false) => {
    try {
      setInitializing(true)
      setError(null)

      const response = await fetch('/api/ai/initialize', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          documentId,
          forceReinitialize
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'AI initialization failed')
      }

      if (result.success) {
        toast.success('AI tutor initialized successfully!')
        
        // Refresh status
        await fetchStatus()
        
        // Notify parent component
        if (onInitializationComplete) {
          onInitializationComplete(result.sessionId, result.initialResponse)
        }
      } else if (result.alreadyInitialized) {
        toast.info('AI tutor is already initialized for this document')
      }

    } catch (err) {
      console.error('AI initialization failed:', err)
      const errorMessage = err instanceof Error ? err.message : 'Unknown error'
      setError(errorMessage)
      toast.error(`AI initialization failed: ${errorMessage}`)
    } finally {
      setInitializing(false)
    }
  }

  /**
   * Get status badge based on current state
   */
  const getStatusBadge = () => {
    if (!status) return null

    if (status.isInitialized) {
      return (
        <Badge variant="default" className="bg-green-100 text-green-800 border-green-200">
          <CheckCircle className="w-3 h-3 mr-1" />
          AI Ready
        </Badge>
      )
    }

    if (status.isReady && status.canInitialize) {
      return (
        <Badge variant="secondary" className="bg-blue-100 text-blue-800 border-blue-200">
          <Brain className="w-3 h-3 mr-1" />
          Ready to Initialize
        </Badge>
      )
    }

    if (status.documentStatus === 'PROCESSING') {
      return (
        <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-200">
          <Loader2 className="w-3 h-3 mr-1 animate-spin" />
          Processing Document
        </Badge>
      )
    }

    return (
      <Badge variant="destructive" className="bg-red-100 text-red-800 border-red-200">
        <AlertCircle className="w-3 h-3 mr-1" />
        Not Ready
      </Badge>
    )
  }

  /**
   * Get status description text
   */
  const getStatusDescription = () => {
    if (!status) return 'Loading status...'

    if (status.isInitialized) {
      return 'AI tutor is ready and waiting for your questions. You can start learning immediately!'
    }

    if (status.isReady && status.canInitialize) {
      return `Document processed into ${status.totalChunks} sections. Ready to initialize AI tutor.`
    }

    if (status.documentStatus === 'PROCESSING') {
      return 'Document is being processed and broken into learning sections. Please wait...'
    }

    if (status.documentStatus === 'ERROR') {
      return 'Document processing failed. Please try uploading the document again.'
    }

    return 'Document is not ready for AI initialization.'
  }

  // Fetch status on component mount and when documentId changes
  useEffect(() => {
    if (documentId) {
      fetchStatus()
    }
  }, [documentId])

  // Auto-refresh status while document is processing
  useEffect(() => {
    if (status?.documentStatus === 'PROCESSING') {
      const interval = setInterval(fetchStatus, 3000) // Check every 3 seconds
      return () => clearInterval(interval)
    }
  }, [status?.documentStatus])

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="w-6 h-6 animate-spin mr-2" />
          <span>Loading AI status...</span>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Brain className="w-5 h-5" />
              AI Tutor Status
            </CardTitle>
            <CardDescription>
              {documentName}
            </CardDescription>
          </div>
          {getStatusBadge()}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          {getStatusDescription()}
        </p>

        {error && (
          <div className="p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">{error}</p>
          </div>
        )}

        <div className="flex gap-2">
          {status?.canInitialize && (
            <Button 
              onClick={() => initializeAI(false)}
              disabled={initializing}
              className="flex items-center gap-2"
            >
              {initializing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Brain className="w-4 h-4" />
              )}
              Initialize AI Tutor
            </Button>
          )}

          {status?.isInitialized && (
            <Button 
              variant="outline"
              onClick={() => initializeAI(true)}
              disabled={initializing}
              className="flex items-center gap-2"
            >
              {initializing ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <RefreshCw className="w-4 h-4" />
              )}
              Reinitialize
            </Button>
          )}

          <Button 
            variant="ghost"
            onClick={fetchStatus}
            disabled={loading}
            className="flex items-center gap-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>

        {status && (
          <div className="text-xs text-muted-foreground space-y-1">
            <div>Document Status: {status.documentStatus}</div>
            <div>Total Sections: {status.totalChunks}</div>
            <div>AI Initialized: {status.isInitialized ? 'Yes' : 'No'}</div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
