generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("POSTGRES_PRISMA_URL")
}

model User {
  email     String     @unique
  createdAt DateTime   @default(now()) @map("created_at")
  updatedAt DateTime   @updatedAt @map("updated_at")
  id        String     @id @db.Uuid
  chunks    Chunk[]
  documents Document[]
  profile   Profile?
  progress  Progress[]

  @@map("user")
}

model Profile {
  id        String   @id @default(cuid())
  fullName  String?
  avatarUrl String?  @map("avatar_url")
  school    String?
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")
  userId    String   @unique @db.Uuid
  user      User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("profiles")
}

model Document {
  id           String     @id @default(cuid())
  fileName     String     @map("file_name")
  filePath     String     @map("file_path")
  totalChunks  Int        @default(0) @map("total_chunks")
  status       String     @default("PROCESSING") // "PROCESSING" | "READY" | "ERROR"
  errorMessage String?    @map("error_message")
  createdAt    DateTime   @default(now()) @map("created_at")
  updatedAt    DateTime   @updatedAt @map("updated_at")
  userId       String     @db.Uuid
  chunks       Chunk[]
  user         User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  progress     Progress[]

  @@map("documents")
}

model Chunk {
  id         String   @id @default(cuid())
  documentId String   @map("document_id")
  chunkIndex Int      @map("chunk_index")
  pageNumber Int?     @map("page_number")
  content    String
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  userId     String?  @map("user_id") @db.Uuid
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user       User?    @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([documentId, chunkIndex], name: "documentId_chunkIndex")
  @@map("chunks")
}

model Progress {
  id           String   @id @default(cuid())
  documentId   String   @map("document_id")
  currentChunk Int      @default(0) @map("current_chunk")
  sessionId    String?  @map("session_id")
  createdAt    DateTime @default(now()) @map("created_at")
  updatedAt    DateTime @updatedAt @map("updated_at")
  userId       String   @map("user_id") @db.Uuid
  document     Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, documentId])
  @@map("progress")
}
