import React from 'react'
import { cn } from '@/lib/utils'

interface AIThinkingProps {
  className?: string
  showText?: boolean
}

export const AIThinking: React.FC<AIThinkingProps> = ({ 
  className, 
  showText = true 
}) => {
  return (
    <div className={cn("flex items-center space-x-2 py-2", className)}>
      {/* Animated dots */}
      <div className="flex space-x-1">
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.3s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce [animation-delay:-0.15s]"></div>
        <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
      </div>
      
      {/* Optional text */}
      {showText && (
        <span className="text-gray-500 text-sm">AI is thinking...</span>
      )}
    </div>
  )
}

export default AIThinking
