import { dbService } from '@/lib/db-service'
import { memoryService } from './memory-service'
import { AIProviderFactory } from './providers'
import { EducationalPromptBuilder } from './educational-prompts'

/**
 * Document AI Initialization Service
 * Automatically initializes AI tutoring when documents become ready
 */

/**
 * AI initialization result containing session info and initial response
 */
export interface AIInitializationResult {
  sessionId: string
  initialResponse: string
  chunkContent: string
  success: boolean
  error?: string
}

/**
 * Document AI Initializer class
 * Handles automatic AI initialization after document processing completion
 */
export class DocumentAIInitializer {
  
  /**
   * Initialize AI tutoring for a processed document
   * Creates memory session, generates initial AI response, and sets up progress tracking
   * 
   * @param documentId - Document identifier
   * @param userId - User identifier
   * @param fileName - Original file name
   * @returns AI initialization result
   */
  async initializeAIForDocument(
    documentId: string, 
    userId: string, 
    fileName: string
  ): Promise<AIInitializationResult> {
    try {
      console.log(`🤖 Starting AI initialization for document ${documentId}`)

      // Step 0: Clean up any existing session data to ensure fresh start
      await this.cleanupExistingSession(userId, documentId)

      // Step 1: Retrieve the first chunk
      const firstChunk = await this.getFirstChunk(documentId)
      if (!firstChunk) {
        throw new Error('No first chunk found for AI initialization')
      }

      // Step 2: Get document info for context
      const document = await dbService.document.findUnique({
        where: { id: documentId }
      })
      
      if (!document) {
        throw new Error('Document not found')
      }

      // Step 3: Initialize fresh memory session
      const sessionId = await memoryService.initializeSession(userId, documentId)
      console.log(`🧠 Fresh memory session initialized: ${sessionId}`)

      // Step 4: Create or update progress record
      await this.createProgressRecord(userId, documentId, sessionId)

      // Step 5: Generate initial AI response
      const initialResponse = await this.generateInitialAIResponse(
        firstChunk.content,
        fileName,
        sessionId,
        userId,
        documentId
      )

      console.log(`✅ AI initialization completed for document ${documentId}`)

      return {
        sessionId,
        initialResponse,
        chunkContent: firstChunk.content,
        success: true
      }

    } catch (error) {
      console.error(`❌ AI initialization failed for document ${documentId}:`, error)
      
      return {
        sessionId: '',
        initialResponse: '',
        chunkContent: '',
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }
    }
  }

  /**
   * Clean up any existing session data for a user and document
   * Ensures fresh start for new AI initialization
   * 
   * @param userId - User identifier
   * @param documentId - Document identifier
   */
  private async cleanupExistingSession(userId: string, documentId: string): Promise<void> {
    try {
      console.log(`🧹 Cleaning up existing session data for user ${userId}, document ${documentId}`)
      
      // Check for existing progress record
      const existingProgress = await dbService.progress.findUnique({
        where: {
          userId_documentId: {
            userId,
            documentId
          }
        }
      })
      
      if (existingProgress?.sessionId) {
        console.log(`🗑️ Found existing session ${existingProgress.sessionId}, cleaning up...`)
        
        // Delete all memories associated with the session
        await memoryService.deleteAllMemories(existingProgress.sessionId)
        
        // Clear from session state manager
        const { sessionStateManager } = await import('@/lib/session-state')
        sessionStateManager.clearSession(documentId)
        
        console.log(`✅ Cleaned up existing session: ${existingProgress.sessionId}`)
      } else {
        console.log(`ℹ️ No existing session found for cleanup`)
      }
    } catch (error) {
      console.warn('⚠️ Failed to cleanup existing session (continuing with initialization):', error)
      // Don't throw error - continue with initialization even if cleanup fails
    }
  }

  /**
   * Retrieve the first chunk of a document
   * 
   * @param documentId - Document identifier
   * @returns First chunk or null if not found
   */
  private async getFirstChunk(documentId: string) {
    return await dbService.chunk.findUnique({
      where: {
        documentId_chunkIndex: {
          documentId,
          chunkIndex: 0
        }
      }
    })
  }

  /**
   * Create or update progress record for user and document
   * 
   * @param userId - User identifier
   * @param documentId - Document identifier
   * @param sessionId - Memory session identifier
   */
  private async createProgressRecord(userId: string, documentId: string, sessionId: string) {
    await dbService.progress.upsert({
      where: {
        userId_documentId: {
          userId,
          documentId
        }
      },
      update: {
        currentChunk: 0,
        sessionId
      },
      create: {
        documentId,
        userId,
        currentChunk: 0,
        sessionId
      }
    })
  }

  /**
   * Generate initial AI response for document introduction
   * Uses educational prompt system to create engaging introduction
   * 
   * @param chunkContent - Content of first chunk
   * @param fileName - Original file name
   * @param sessionId - Memory session identifier
   * @param userId - User identifier
   * @param documentId - Document identifier
   * @returns Initial AI response
   */
  private async generateInitialAIResponse(
    chunkContent: string,
    fileName: string,
    sessionId: string,
    userId: string,
    documentId: string
  ): Promise<string> {
    try {
      // Create AI provider instance
      const aiProvider = AIProviderFactory.createProvider()
      await aiProvider.initialize()

      // Build document introduction prompt
      const introductionPrompt = EducationalPromptBuilder.buildDocumentIntroductionPrompt(
        fileName,
        chunkContent,
        'intermediate' // Default difficulty level
      )

      // Create conversation context for AI
      const context = {
        userId,
        documentId,
        chunkIndex: 0,
        chunkContent,
        sessionId,
        currentQuery: 'Initialize learning session'
      }

      // Generate streaming response and collect full text
      let fullResponse = ''
      const responseGenerator = aiProvider.generateResponse(introductionPrompt, context)
      
      for await (const token of responseGenerator) {
        fullResponse += token
      }

      // Store the initial interaction in memory using synchronous mode for immediate availability
      await memoryService.addConversationMessage(sessionId, 'Initialize learning session', 'user', true)
      await memoryService.addConversationMessage(sessionId, fullResponse, 'assistant', true)

      console.log(`🎯 Generated initial AI response (${fullResponse.length} characters)`)
      return fullResponse

    } catch (error) {
      console.error('Failed to generate initial AI response:', error)
      
      // Fallback to basic introduction
      return this.getFallbackIntroduction(fileName, chunkContent)
    }
  }

  /**
   * Get fallback introduction when AI generation fails
   * 
   * @param fileName - Original file name
   * @param _chunkContent - Content of first chunk
   * @returns Basic introduction text
   */
  private getFallbackIntroduction(fileName: string, _chunkContent: string): string {
    return `Welcome to your learning session with "${fileName}"! 

I'm here to guide you through this material using the Socratic method, which means I'll help you discover insights through thoughtful questions rather than just giving you answers.

Let's start by exploring the first section together. Take a moment to read through the content, and then I'd like to ask: What stands out to you as the most important concept in this opening section?

This approach will help ensure you truly understand the material rather than just memorizing it. Are you ready to begin this learning journey?`
  }

  /**
   * Check if a document is ready for AI initialization
   * 
   * @param documentId - Document identifier
   * @returns True if document is ready for AI initialization
   */
  async isDocumentReadyForAI(documentId: string): Promise<boolean> {
    try {
      const document = await dbService.document.findUnique({
        where: { id: documentId }
      })

      return document?.status === 'READY' && (document.totalChunks || 0) > 0
    } catch (error) {
      console.error('Error checking document readiness:', error)
      return false
    }
  }

  /**
   * Get AI initialization status for a document
   * 
   * @param documentId - Document identifier
   * @param userId - User identifier
   * @returns True if AI has been initialized for this document and user
   */
  async isAIInitialized(documentId: string, userId: string): Promise<boolean> {
    try {
      const progress = await dbService.progress.findUnique({
        where: {
          userId_documentId: {
            userId,
            documentId
          }
        }
      })

      return !!progress?.sessionId
    } catch (error) {
      console.error('Error checking AI initialization status:', error)
      return false
    }
  }

  /**
   * Reinitialize AI for a document (useful for testing or recovery)
   * 
   * @param documentId - Document identifier
   * @param userId - User identifier
   * @param fileName - Original file name
   * @returns AI initialization result
   */
  async reinitializeAI(
    documentId: string, 
    userId: string, 
    fileName: string
  ): Promise<AIInitializationResult> {
    console.log(`🔄 Reinitializing AI for document ${documentId}`)
    
    // The cleanup is now handled in initializeAIForDocument
    // Initialize fresh AI session
    return await this.initializeAIForDocument(documentId, userId, fileName)
  }
}

// Export singleton instance
export const documentAIInitializer = new DocumentAIInitializer()
