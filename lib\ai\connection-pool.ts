/**
 * AI Provider Connection Pool
 * Manages connection pooling and reuse for AI provider clients
 */

import { AIProvider, OpenAIProvider, AnthropicProvider } from './providers'

export interface PoolConfig {
    maxConnections: number
    idleTimeout: number // milliseconds
    connectionTimeout: number // milliseconds
    retryAttempts: number
}

export interface PooledConnection {
    provider: AIProvider
    id: string
    created: number
    lastUsed: number
    inUse: boolean
    usageCount: number
}

export class ConnectionPool {
    private pools = new Map<string, PooledConnection[]>()
    private config: PoolConfig
    private connectionCounter = 0

    constructor(config?: Partial<PoolConfig>) {
        this.config = {
            maxConnections: 5,
            idleTimeout: 5 * 60 * 1000, // 5 minutes
            connectionTimeout: 30 * 1000, // 30 seconds
            retryAttempts: 3,
            ...config
        }

        // Start cleanup interval
        setInterval(() => this.cleanup(), 60 * 1000) // Every minute
    }

    /**
     * Get a connection from the pool
     */
    async getConnection(providerType: 'openai' | 'anthropic'): Promise<PooledConnection> {
        const pool = this.getPool(providerType)
        
        // Try to find an available connection
        const available = pool.find(conn => !conn.inUse && !this.isExpired(conn))
        
        if (available) {
            available.inUse = true
            available.lastUsed = Date.now()
            available.usageCount++
            console.log(`🔄 Reusing ${providerType} connection ${available.id}`)
            return available
        }

        // Create new connection if under limit
        if (pool.length < this.config.maxConnections) {
            const connection = await this.createConnection(providerType)
            pool.push(connection)
            console.log(`🆕 Created new ${providerType} connection ${connection.id}`)
            return connection
        }

        // Wait for a connection to become available
        return this.waitForConnection(providerType)
    }

    /**
     * Release a connection back to the pool
     */
    releaseConnection(connection: PooledConnection): void {
        connection.inUse = false
        connection.lastUsed = Date.now()
        console.log(`🔓 Released connection ${connection.id}`)
    }

    /**
     * Get or create pool for provider type
     */
    private getPool(providerType: string): PooledConnection[] {
        if (!this.pools.has(providerType)) {
            this.pools.set(providerType, [])
        }
        return this.pools.get(providerType)!
    }

    /**
     * Create a new connection
     */
    private async createConnection(providerType: 'openai' | 'anthropic'): Promise<PooledConnection> {
        const id = `${providerType}-${++this.connectionCounter}`
        
        let provider: AIProvider
        
        switch (providerType) {
            case 'openai':
                provider = new OpenAIProvider()
                break
            case 'anthropic':
                provider = new AnthropicProvider()
                break
            default:
                throw new Error(`Unsupported provider type: ${providerType}`)
        }

        // Initialize the provider with timeout
        const initPromise = provider.initialize()
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Connection timeout')), this.config.connectionTimeout)
        })

        try {
            await Promise.race([initPromise, timeoutPromise])
        } catch (error) {
            console.error(`❌ Failed to initialize ${providerType} connection:`, error)
            throw error
        }

        const connection: PooledConnection = {
            provider,
            id,
            created: Date.now(),
            lastUsed: Date.now(),
            inUse: true,
            usageCount: 1
        }

        return connection
    }

    /**
     * Wait for an available connection
     */
    private async waitForConnection(providerType: string): Promise<PooledConnection> {
        const maxWaitTime = this.config.connectionTimeout
        const checkInterval = 100 // Check every 100ms
        const startTime = Date.now()

        while (Date.now() - startTime < maxWaitTime) {
            const pool = this.getPool(providerType)
            const available = pool.find(conn => !conn.inUse && !this.isExpired(conn))
            
            if (available) {
                available.inUse = true
                available.lastUsed = Date.now()
                available.usageCount++
                console.log(`⏳ Got available ${providerType} connection ${available.id} after waiting`)
                return available
            }

            await new Promise(resolve => setTimeout(resolve, checkInterval))
        }

        throw new Error(`Connection pool timeout for ${providerType}`)
    }

    /**
     * Check if connection is expired
     */
    private isExpired(connection: PooledConnection): boolean {
        return Date.now() - connection.lastUsed > this.config.idleTimeout
    }

    /**
     * Clean up expired connections
     */
    private cleanup(): void {
        let totalRemoved = 0

        for (const [providerType, pool] of this.pools.entries()) {
            const initialSize = pool.length
            
            // Remove expired and unused connections
            const activeConnections = pool.filter(conn => {
                const expired = this.isExpired(conn)
                const shouldRemove = expired && !conn.inUse
                
                if (shouldRemove) {
                    console.log(`🗑️ Removing expired ${providerType} connection ${conn.id}`)
                }
                
                return !shouldRemove
            })

            this.pools.set(providerType, activeConnections)
            totalRemoved += initialSize - activeConnections.length
        }

        if (totalRemoved > 0) {
            console.log(`🧹 Connection pool cleanup: removed ${totalRemoved} expired connections`)
        }
    }

    /**
     * Execute operation with pooled connection
     */
    async withConnection<T>(
        providerType: 'openai' | 'anthropic',
        operation: (provider: AIProvider) => Promise<T>
    ): Promise<T> {
        const connection = await this.getConnection(providerType)
        
        try {
            return await operation(connection.provider)
        } finally {
            this.releaseConnection(connection)
        }
    }

    /**
     * Get pool statistics
     */
    getStats(): Record<string, {
        total: number
        inUse: number
        available: number
        totalUsage: number
        avgUsagePerConnection: number
    }> {
        const stats: Record<string, any> = {}

        for (const [providerType, pool] of this.pools.entries()) {
            const inUse = pool.filter(conn => conn.inUse).length
            const totalUsage = pool.reduce((sum, conn) => sum + conn.usageCount, 0)
            
            stats[providerType] = {
                total: pool.length,
                inUse,
                available: pool.length - inUse,
                totalUsage,
                avgUsagePerConnection: pool.length > 0 ? totalUsage / pool.length : 0
            }
        }

        return stats
    }

    /**
     * Close all connections and clear pools
     */
    async shutdown(): Promise<void> {
        console.log('🔌 Shutting down connection pools...')
        
        for (const [providerType, pool] of this.pools.entries()) {
            console.log(`Closing ${pool.length} ${providerType} connections`)
            // Note: AI providers don't typically have explicit close methods
            // but we clear the references
        }

        this.pools.clear()
        console.log('✅ Connection pools shut down')
    }

    /**
     * Force refresh all connections (useful for credential rotation)
     */
    async refreshConnections(): Promise<void> {
        console.log('🔄 Refreshing all connections...')
        
        const oldPools = new Map(this.pools)
        this.pools.clear()
        
        // Wait for any in-use connections to be released
        for (const [providerType, pool] of oldPools.entries()) {
            const inUseConnections = pool.filter(conn => conn.inUse)
            if (inUseConnections.length > 0) {
                console.log(`⏳ Waiting for ${inUseConnections.length} ${providerType} connections to be released...`)
                
                // Wait up to 30 seconds for connections to be released
                const maxWait = 30000
                const startTime = Date.now()
                
                while (inUseConnections.some(conn => conn.inUse) && Date.now() - startTime < maxWait) {
                    await new Promise(resolve => setTimeout(resolve, 1000))
                }
            }
        }
        
        console.log('✅ All connections refreshed')
    }
}

// Export singleton instance
export const connectionPool = new ConnectionPool()
