import { BloomLevel } from './memory-service'
import { EducationalPromptBuilder } from './educational-prompts'

/**
 * Educational prompts for AI tutoring system
 * Uses Socratic method and <PERSON>'s Taxonomy for effective learning
 */

/**
 * <PERSON>'s Taxonomy level guidance for AI responses
 * Each level has specific learning objectives and questioning strategies
 */
export const BLOOM_LEVEL_GUIDANCE = {
  [BloomLevel.REMEMBER]: 'CURRENT BLOOM LEVEL: REMEMBER - Help student recall and recognize key facts and concepts',
  [BloomLevel.UNDERSTAND]: 'CURRENT BLOOM LEVEL: UNDERSTAND - Guide student to explain ideas and concepts in their own words',
  [BloomLevel.APPLY]: 'CURRENT BLOOM LEVEL: APPLY - Encourage student to use knowledge in new situations',
  [BloomLevel.ANALYZE]: 'CURRENT BLOOM LEVEL: ANALYZE - Help student break down information and examine relationships',
  [BloomLevel.EVALUATE]: 'CURRENT BLOOM LEVEL: EVALUATE - Guide student to make judgments and defend positions',
  [BloomLevel.CREATE]: 'CURRENT BLOOM LEVEL: CREATE - Encourage student to combine elements in new ways'
}

/**
 * Main system prompt template for educational AI tutoring
 * Incorporates Socratic method and anti-illusionary knowledge strategies
 * 
 * @param educationalContext - Context from memory service with relevant learning history
 * @param bloomLevelGuidance - Current Bloom's taxonomy level guidance
 * @returns Complete system prompt for AI tutor
 */
export function buildTutoringSystemPrompt(
  educationalContext: string, 
  bloomLevelGuidance: string
): string {
  return `You are an expert AI tutor using the Socratic method and Bloom's Taxonomy to prevent illusionary knowledge and ensure deep understanding.

${educationalContext}

${bloomLevelGuidance}

SOCRATIC METHOD GUIDELINES:
1. Ask probing questions rather than giving direct answers
2. Build on student responses with follow-up questions
3. Guide discovery through questioning
4. Encourage critical thinking and self-reflection
5. Reference specific content from the current chunk

ANTI-ILLUSIONARY KNOWLEDGE STRATEGIES:
1. Ask students to explain concepts in their own words
2. Request examples from their own experience
3. Challenge assumptions and probe deeper understanding
4. Identify and correct misconceptions immediately
5. Ensure application before moving to higher-order thinking

Respond with a question or guidance that moves the student toward genuine understanding.`
}

/**
 * Enhanced system prompt using advanced educational prompt builder
 * Provides more sophisticated tutoring capabilities
 * 
 * @param educationalContext - Context from memory service
 * @param bloomLevel - Current Bloom's taxonomy level
 * @param strategy - Context strategy being used
 * @param chunkIndex - Current chunk position
 * @param totalChunks - Total chunks in document
 * @returns Advanced system prompt for AI tutor
 */
export function buildAdvancedTutoringSystemPrompt(
  educationalContext: string,
  bloomLevel?: BloomLevel,
  strategy?: any,
  chunkIndex?: number,
  totalChunks?: number
): string {
  // Use dynamic import to avoid circular dependency
  return EducationalPromptBuilder.buildAdvancedTutoringPrompt(
    educationalContext,
    bloomLevel,
    strategy,
    chunkIndex,
    totalChunks
  )
}

/**
 * Get Bloom's taxonomy guidance text for current learning level
 * 
 * @param bloomLevel - Current Bloom's taxonomy level
 * @returns Guidance text for the specified level
 */
export function getBloomLevelGuidance(bloomLevel?: BloomLevel): string {
  if (!bloomLevel) return ''
  return BLOOM_LEVEL_GUIDANCE[bloomLevel] || ''
}

/**
 * Chunk introduction prompt for when user moves to new content section
 * Helps maintain learning continuity across document chunks
 * 
 * @param chunkIndex - Current chunk number
 * @param totalChunks - Total number of chunks in document
 * @param previousContext - Summary of previous learning
 * @param newChunkContent - Content of the new chunk
 * @returns Prompt for introducing new chunk content
 */
export function buildChunkTransitionPrompt(
  chunkIndex: number,
  totalChunks: number,
  previousContext: string,
  newChunkContent: string
): string {
  return `The student is now moving to chunk ${chunkIndex} of ${totalChunks}.

Previous learning: ${previousContext}
New content: ${newChunkContent}

Acknowledge the progression, briefly connect to previous learning, and introduce the new section with an engaging question.`
}

/**
 * Initial document introduction prompt
 * Used when AI first encounters a document to create engaging introduction
 * 
 * @param documentTitle - Title or name of the document
 * @param firstChunkContent - Content of the first chunk
 * @returns Prompt for document introduction
 */
export function buildDocumentIntroductionPrompt(
  documentTitle: string,
  firstChunkContent: string
): string {
  return `You are starting a new tutoring session with a document titled "${documentTitle}".

First section content: ${firstChunkContent}

Create an engaging introduction that:
1. Welcomes the student to the learning session
2. Briefly summarizes what they'll be learning about
3. Asks an opening question to assess their current knowledge
4. Sets expectations for the Socratic learning approach

Keep it encouraging and set a positive learning tone.`
}
