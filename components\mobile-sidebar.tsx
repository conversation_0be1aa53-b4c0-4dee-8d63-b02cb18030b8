"use client"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Settings, LogOut, GraduationCap } from "lucide-react"
import { DocumentList } from "@/components/dashboard/document-list"

import { createClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import type { User as SupabaseUser } from "@supabase/supabase-js"

interface MobileSidebarProps {
  user: SupabaseUser
}

export function MobileSidebar({ user }: MobileSidebarProps) {
  const [refreshDocuments] = useState(0)
  const [loadingSignOut, setLoadingSignOut] = useState(false)
  const supabase = createClient()
  const router = useRouter()


  const handleSignOut = async () => {
    setLoadingSignOut(true)
    await supabase.auth.signOut()
    router.push("/auth")
    router.refresh()
  }

  const getInitials = (email: string) => {
    return email.split("@")[0].slice(0, 2).toUpperCase()
  }

  return (
    <>
      <div className="h-full w-64 bg-[#F9FAFB] text-sidebar-foreground border-r border-sidebar-border shadow-sm flex flex-col">
        {/* Header */}
        <div className="border-b border-sidebar-border">
          <div className="flex items-center gap-3 px-4 py-3">
            <div className="p-2 bg-primary rounded-lg border border-white">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-semibold text-foreground font-heading">Guided Tutor</span>
          </div>
          <div className="px-4 pb-6 pt-2">
            <Button
              onClick={() => window.dispatchEvent(new CustomEvent("navigateToUpload"))}
              className="bg-primary hover:bg-primary/90 text-white rounded-lg font-body w-full px-4 py-3 text-sm font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>New Document</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden">
          <div className="p-2">
            <DocumentList refreshTrigger={refreshDocuments} />
          </div>
        </div>

        {/* Footer */}
        <div className="border-t border-sidebar-border p-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="w-full justify-start p-2">
                <Avatar className="h-7 w-7 mr-2">
                  <AvatarFallback className="bg-primary text-primary-foreground">
                    {getInitials(user.email || "")}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate font-body">{user.email}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="top"
              className="w-[--radix-popper-anchor-width] bg-white text-card-foreground border border-border shadow-sm"
            >
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground font-body">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border" />
              <DropdownMenuItem
                onClick={handleSignOut}
                disabled={loadingSignOut}
                className="text-destructive hover:bg-destructive/10 hover:text-destructive font-body"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </>
  )
}
