/**
 * AI Service Error Handler
 * Provides comprehensive error handling and fallback mechanisms for AI services
 */

export enum ErrorType {
    PROVIDER_UNAVAILABLE = 'PROVIDER_UNAVAILABLE',
    API_RATE_LIMIT = 'API_RATE_LIMIT',
    AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
    MEMORY_SERVICE_ERROR = 'MEMORY_SERVICE_ERROR',
    NETWORK_ERROR = 'NETWORK_ERROR',
    TIMEOUT_ERROR = 'TIMEOUT_ERROR',
    UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface AIError {
    type: ErrorType
    message: string
    originalError?: Error
    provider?: string
    retryable: boolean
    fallbackAvailable: boolean
}

export class AIErrorHandler {
    private static readonly MAX_RETRIES = 3
    private static readonly RETRY_DELAYS = [1000, 2000, 4000] // Progressive backoff

    /**
     * Classify error type based on error details
     */
    static classifyError(error: any, provider?: string): AIError {
        const errorMessage = error?.message || error?.toString() || 'Unknown error'
        
        // OpenAI specific errors
        if (provider === 'openai') {
            if (error?.status === 401 || errorMessage.toLowerCase().includes('authentication')) {
                return {
                    type: ErrorType.AUTHENTICATION_ERROR,
                    message: 'OpenAI API authentication failed. Please check your API key.',
                    originalError: error,
                    provider,
                    retryable: false,
                    fallbackAvailable: true
                }
            }
            
            if (error?.status === 429 || errorMessage.includes('rate limit')) {
                return {
                    type: ErrorType.API_RATE_LIMIT,
                    message: 'OpenAI API rate limit exceeded. Please try again later.',
                    originalError: error,
                    provider,
                    retryable: true,
                    fallbackAvailable: true
                }
            }
            
            if (error?.status >= 500 || errorMessage.includes('server error')) {
                return {
                    type: ErrorType.PROVIDER_UNAVAILABLE,
                    message: 'OpenAI service is temporarily unavailable.',
                    originalError: error,
                    provider,
                    retryable: true,
                    fallbackAvailable: true
                }
            }
        }

        // Anthropic specific errors
        if (provider === 'anthropic') {
            if (error?.status === 401 || errorMessage.toLowerCase().includes('authentication')) {
                return {
                    type: ErrorType.AUTHENTICATION_ERROR,
                    message: 'Anthropic API authentication failed. Please check your API key.',
                    originalError: error,
                    provider,
                    retryable: false,
                    fallbackAvailable: true
                }
            }
            
            if (error?.status === 429 || errorMessage.includes('rate limit')) {
                return {
                    type: ErrorType.API_RATE_LIMIT,
                    message: 'Anthropic API rate limit exceeded. Please try again later.',
                    originalError: error,
                    provider,
                    retryable: true,
                    fallbackAvailable: true
                }
            }
            
            if (error?.status >= 500 || errorMessage.includes('server error')) {
                return {
                    type: ErrorType.PROVIDER_UNAVAILABLE,
                    message: 'Anthropic service is temporarily unavailable.',
                    originalError: error,
                    provider,
                    retryable: true,
                    fallbackAvailable: true
                }
            }
        }

        // Memory service errors
        if (errorMessage.includes('Mem0') || errorMessage.includes('memory')) {
            return {
                type: ErrorType.MEMORY_SERVICE_ERROR,
                message: 'Memory service is temporarily unavailable. Chat will continue without memory.',
                originalError: error,
                provider: 'mem0',
                retryable: true,
                fallbackAvailable: true
            }
        }

        // Timeout errors (check before network errors)
        if (errorMessage.includes('timeout') || errorMessage.includes('ETIMEDOUT')) {
            return {
                type: ErrorType.TIMEOUT_ERROR,
                message: 'Request timed out. Please try again.',
                originalError: error,
                provider,
                retryable: true,
                fallbackAvailable: true
            }
        }

        // Network errors
        if (errorMessage.includes('network') || errorMessage.includes('connection') || 
            errorMessage.includes('ECONNREFUSED')) {
            return {
                type: ErrorType.NETWORK_ERROR,
                message: 'Network connection error. Please check your internet connection.',
                originalError: error,
                provider,
                retryable: true,
                fallbackAvailable: true
            }
        }

        // Default unknown error
        return {
            type: ErrorType.UNKNOWN_ERROR,
            message: `An unexpected error occurred: ${errorMessage}`,
            originalError: error,
            provider,
            retryable: true,
            fallbackAvailable: true
        }
    }

    /**
     * Execute function with retry logic
     */
    static async withRetry<T>(
        fn: () => Promise<T>,
        provider?: string,
        maxRetries: number = AIErrorHandler.MAX_RETRIES
    ): Promise<T> {
        let lastError: any

        for (let attempt = 0; attempt <= maxRetries; attempt++) {
            try {
                return await fn()
            } catch (error) {
                lastError = error
                const aiError = AIErrorHandler.classifyError(error, provider)
                
                // Don't retry non-retryable errors
                if (!aiError.retryable || attempt === maxRetries) {
                    throw aiError
                }

                // Wait before retrying with progressive backoff
                if (attempt < maxRetries) {
                    const delay = AIErrorHandler.RETRY_DELAYS[attempt] || 4000
                    console.log(`⚠️ Attempt ${attempt + 1} failed for ${provider}, retrying in ${delay}ms...`)
                    await new Promise(resolve => setTimeout(resolve, delay))
                }
            }
        }

        throw AIErrorHandler.classifyError(lastError, provider)
    }

    /**
     * Get user-friendly error message
     */
    static getUserMessage(error: AIError): string {
        switch (error.type) {
            case ErrorType.PROVIDER_UNAVAILABLE:
                return `${error.provider} is temporarily unavailable. We're trying an alternative provider.`
            
            case ErrorType.API_RATE_LIMIT:
                return 'Too many requests. Please wait a moment before trying again.'
            
            case ErrorType.AUTHENTICATION_ERROR:
                return 'Authentication failed. Please contact support if this persists.'
            
            case ErrorType.MEMORY_SERVICE_ERROR:
                return 'Memory service is unavailable, but chat will continue normally.'
            
            case ErrorType.NETWORK_ERROR:
                return 'Network connection issue. Please check your internet connection.'
            
            case ErrorType.TIMEOUT_ERROR:
                return 'Request timed out. Please try again.'
            
            default:
                return 'Something went wrong. Please try again or contact support if the issue persists.'
        }
    }

    /**
     * Log error with appropriate level
     */
    static logError(error: AIError, context?: string): void {
        const logContext = context ? `[${context}] ` : ''
        const providerInfo = error.provider ? ` (${error.provider})` : ''
        
        console.error(`❌ ${logContext}${error.type}${providerInfo}: ${error.message}`)
        
        if (error.originalError) {
            console.error('Original error:', error.originalError)
        }
    }

    /**
     * Check if error should trigger fallback
     */
    static shouldFallback(error: AIError): boolean {
        return error.fallbackAvailable && (
            error.type === ErrorType.PROVIDER_UNAVAILABLE ||
            error.type === ErrorType.AUTHENTICATION_ERROR ||
            error.type === ErrorType.API_RATE_LIMIT
        )
    }
}
