/**
 * Test Results Processor for AI Chat Integration Tests
 * Processes and formats test results for reporting
 */

const fs = require('fs')
const path = require('path')

module.exports = (results) => {
  const timestamp = new Date().toISOString()
  const reportDir = path.join(process.cwd(), 'test-reports', 'ai-integration')
  
  // Ensure report directory exists
  if (!fs.existsSync(reportDir)) {
    fs.mkdirSync(reportDir, { recursive: true })
  }
  
  // Generate detailed report
  const report = {
    timestamp,
    summary: {
      totalTests: results.numTotalTests,
      passedTests: results.numPassedTests,
      failedTests: results.numFailedTests,
      skippedTests: results.numPendingTests,
      duration: results.testResults.reduce((sum, result) => sum + (result.perfStats?.end - result.perfStats?.start || 0), 0),
      successRate: results.numTotalTests > 0 ? (results.numPassedTests / results.numTotalTests * 100).toFixed(2) : 0
    },
    testSuites: results.testResults.map(testResult => ({
      name: testResult.testFilePath.split('/').pop(),
      status: testResult.numFailingTests > 0 ? 'failed' : 'passed',
      duration: testResult.perfStats?.end - testResult.perfStats?.start || 0,
      tests: testResult.testResults.map(test => ({
        name: test.fullName,
        status: test.status,
        duration: test.duration || 0,
        error: test.failureMessages.length > 0 ? test.failureMessages[0] : null
      }))
    })),
    coverage: results.coverageMap ? {
      statements: calculateCoveragePercentage(results.coverageMap, 'statements'),
      branches: calculateCoveragePercentage(results.coverageMap, 'branches'),
      functions: calculateCoveragePercentage(results.coverageMap, 'functions'),
      lines: calculateCoveragePercentage(results.coverageMap, 'lines')
    } : null,
    recommendations: generateRecommendations(results)
  }
  
  // Write JSON report
  const jsonReportPath = path.join(reportDir, `ai-integration-report-${timestamp.replace(/[:.]/g, '-')}.json`)
  fs.writeFileSync(jsonReportPath, JSON.stringify(report, null, 2))
  
  // Write HTML report
  const htmlReport = generateHTMLReport(report)
  const htmlReportPath = path.join(reportDir, `ai-integration-report-${timestamp.replace(/[:.]/g, '-')}.html`)
  fs.writeFileSync(htmlReportPath, htmlReport)
  
  // Console output
  console.log('\n' + '='.repeat(80))
  console.log('🤖 AI CHAT INTEGRATION TEST RESULTS')
  console.log('='.repeat(80))
  console.log(`📊 Tests: ${report.summary.totalTests} total, ${report.summary.passedTests} passed, ${report.summary.failedTests} failed`)
  console.log(`⏱️  Duration: ${(report.summary.duration / 1000).toFixed(2)}s`)
  console.log(`📈 Success Rate: ${report.summary.successRate}%`)
  
  if (report.coverage) {
    console.log(`📋 Coverage: ${report.coverage.statements}% statements, ${report.coverage.branches}% branches`)
  }
  
  console.log(`📄 Reports saved to: ${reportDir}`)
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 Recommendations:')
    report.recommendations.forEach(rec => console.log(`  • ${rec}`))
  }
  
  console.log('='.repeat(80))
  
  return results
}

function calculateCoveragePercentage(coverageMap, type) {
  if (!coverageMap || !coverageMap.data) return 0
  
  let covered = 0
  let total = 0
  
  Object.values(coverageMap.data).forEach(fileCoverage => {
    if (fileCoverage[type]) {
      Object.values(fileCoverage[type]).forEach(count => {
        total++
        if (count > 0) covered++
      })
    }
  })
  
  return total > 0 ? ((covered / total) * 100).toFixed(1) : 0
}

function generateRecommendations(results) {
  const recommendations = []
  
  // Check test coverage
  if (results.coverageMap) {
    const statements = calculateCoveragePercentage(results.coverageMap, 'statements')
    if (statements < 80) {
      recommendations.push(`Increase test coverage (currently ${statements}% statements)`)
    }
  }
  
  // Check for failed tests
  if (results.numFailedTests > 0) {
    recommendations.push('Fix failing tests before deployment')
  }
  
  // Check for slow tests
  const slowTests = results.testResults.filter(result => 
    (result.perfStats?.end - result.perfStats?.start || 0) > 5000
  )
  if (slowTests.length > 0) {
    recommendations.push('Optimize slow test suites for better performance')
  }
  
  // Check for skipped tests
  if (results.numPendingTests > 0) {
    recommendations.push('Complete skipped/pending tests')
  }
  
  return recommendations
}

function generateHTMLReport(report) {
  return `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Chat Integration Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; }
        .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 30px; }
        .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; }
        .metric-value { font-size: 2em; font-weight: bold; color: #007bff; }
        .metric-label { color: #666; margin-top: 5px; }
        .success { color: #28a745; }
        .warning { color: #ffc107; }
        .danger { color: #dc3545; }
        .test-suite { margin-bottom: 20px; border: 1px solid #ddd; border-radius: 6px; }
        .test-suite-header { background: #f8f9fa; padding: 15px; font-weight: bold; }
        .test-case { padding: 10px 15px; border-top: 1px solid #eee; }
        .status-passed { color: #28a745; }
        .status-failed { color: #dc3545; }
        .error-message { background: #f8d7da; color: #721c24; padding: 10px; margin-top: 10px; border-radius: 4px; font-family: monospace; font-size: 0.9em; }
        .recommendations { background: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; border-radius: 6px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 AI Chat Integration Test Report</h1>
            <p>Generated on ${new Date(report.timestamp).toLocaleString()}</p>
        </div>
        
        <div class="summary">
            <div class="metric">
                <div class="metric-value ${report.summary.successRate >= 90 ? 'success' : report.summary.successRate >= 70 ? 'warning' : 'danger'}">${report.summary.successRate}%</div>
                <div class="metric-label">Success Rate</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.summary.totalTests}</div>
                <div class="metric-label">Total Tests</div>
            </div>
            <div class="metric">
                <div class="metric-value success">${report.summary.passedTests}</div>
                <div class="metric-label">Passed</div>
            </div>
            <div class="metric">
                <div class="metric-value danger">${report.summary.failedTests}</div>
                <div class="metric-label">Failed</div>
            </div>
            <div class="metric">
                <div class="metric-value">${(report.summary.duration / 1000).toFixed(2)}s</div>
                <div class="metric-label">Duration</div>
            </div>
        </div>
        
        ${report.coverage ? `
        <div class="summary">
            <div class="metric">
                <div class="metric-value">${report.coverage.statements}%</div>
                <div class="metric-label">Statement Coverage</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.coverage.branches}%</div>
                <div class="metric-label">Branch Coverage</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.coverage.functions}%</div>
                <div class="metric-label">Function Coverage</div>
            </div>
            <div class="metric">
                <div class="metric-value">${report.coverage.lines}%</div>
                <div class="metric-label">Line Coverage</div>
            </div>
        </div>
        ` : ''}
        
        <h2>Test Suites</h2>
        ${report.testSuites.map(suite => `
            <div class="test-suite">
                <div class="test-suite-header">
                    <span class="status-${suite.status}">●</span> ${suite.name} 
                    <span style="float: right;">${(suite.duration / 1000).toFixed(2)}s</span>
                </div>
                ${suite.tests.map(test => `
                    <div class="test-case">
                        <span class="status-${test.status}">●</span> ${test.name}
                        <span style="float: right;">${test.duration || 0}ms</span>
                        ${test.error ? `<div class="error-message">${test.error}</div>` : ''}
                    </div>
                `).join('')}
            </div>
        `).join('')}
        
        ${report.recommendations.length > 0 ? `
        <div class="recommendations">
            <h3>💡 Recommendations</h3>
            <ul>
                ${report.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ul>
        </div>
        ` : ''}
    </div>
</body>
</html>
  `
}