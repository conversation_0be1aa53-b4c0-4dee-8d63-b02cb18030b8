// AI Provider exports
export { 
  <PERSON><PERSON><PERSON>iderF<PERSON>y, 
  OpenAIProvider, 
  AnthropicProvider,
  type AIProvider,
  type AIProviderType,
  type ConversationContext
} from './providers'

// Configuration exports
export { 
  getAIConfig, 
  validateAIConfig, 
  logAIConfig,
  type AIConfig
} from './config'

// Testing utilities
export { testAIProviders } from './test-providers'

// Memory service exports
export { 
  memoryService, 
  MemoryService, 
  BloomLevel,
  type ConversationContext as MemoryConversationContext
} from './memory-service'

// Memory integration utilities
export {
  buildEducationalContext,
  storeConversationMemory,
  initializeMemorySession,
  cleanupMemorySession
} from './memory-integration'

// Educational prompts
export {
  buildTutoringSystemPrompt,
  buildAdvancedTutoringSystemPrompt,
  getBloomLevelGuidance,
  buildChunkTransitionPrompt,
  buildDocumentIntroductionPrompt
} from './prompts'

// Advanced educational prompts
export {
  EducationalPromptBuilder,
  PromptUtils,
  SOCRATIC_TECHNIQUES,
  ANTI_ILLUSION_STRATEGIES
} from './educational-prompts'

// Advanced context engineering
export {
  contextEngineer,
  ContextEngineer,
  ContextStrategy,
  type EducationalCriteria
} from './context-engineering'

// Context strategy selection
export {
  contextStrategySelector,
  ContextStrategySelector
} from './context-strategy-selector'

// Document AI initialization
export {
  documentAIInitializer,
  DocumentAIInitializer,
  type AIInitializationResult
} from './document-ai-initializer'
