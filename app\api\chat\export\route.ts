import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'
import { memoryService } from '@/lib/ai/memory-service'

/**
 * Chat Export API Endpoint
 * Handles exporting conversation history in multiple formats
 */

interface ExportRequest {
  documentId: string
  format?: 'json' | 'text' | 'pdf'
  includeMetadata?: boolean
}

/**
 * POST /api/chat/export
 * Export conversation history for a document
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { 
      documentId, 
      format = 'json', 
      includeMetadata = true 
    }: ExportRequest = await request.json()

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Get document and verify ownership
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Get session ID from progress
    const progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress?.sessionId) {
      return NextResponse.json(
        { error: 'No chat history found for this document' },
        { status: 404 }
      )
    }

    // Export conversation data
    const exportData = await exportConversation(
      progress.sessionId, 
      document, 
      format, 
      includeMetadata
    )

    // Get message count for metadata
    const messageCount = format === 'text' 
      ? (exportData as any).messageCount || 0
      : (exportData as any).messages?.length || 0

    // Return export data with metadata
    return NextResponse.json({
      success: true,
      data: exportData,
      metadata: {
        documentName: document.fileName,
        exportFormat: format,
        exportedAt: new Date().toISOString(),
        totalMessages: messageCount,
        sessionId: progress.sessionId
      }
    })

  } catch (error) {
    console.error('Export conversation error:', error)
    
    return NextResponse.json(
      {
        error: 'Failed to export conversation',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * Export conversation data from memory service
 */
async function exportConversation(
  sessionId: string,
  document: any,
  format: string,
  includeMetadata: boolean
) {
  // Extract user ID and document ID from session ID
  const parts = sessionId.split('-')
  const userId = parts[0]
  const documentId = parts[1]

  // Get all memories for the document
  const memories = await memoryService.getAllMemories(documentId, userId)
  
  // Filter and format conversation messages
  const conversationMessages = memories
    .filter((memory: any) => memory.metadata?.category === 'conversation')
    .sort((a: any, b: any) => 
      new Date(a.metadata?.timestamp || a.created_at).getTime() - 
      new Date(b.metadata?.timestamp || b.created_at).getTime()
    )
    .map((memory: any) => ({
      role: memory.metadata?.role || 'assistant',
      content: memory.memory || memory.text,
      timestamp: memory.metadata?.timestamp || memory.created_at,
      id: memory.id
    }))

  // Build export data structure
  const exportData = {
    document: {
      id: document.id,
      name: document.fileName,
      totalChunks: document.totalChunks
    },
    messages: conversationMessages,
    ...(includeMetadata && {
      sessionInfo: {
        sessionId,
        startedAt: memories[0]?.created_at,
        lastActivity: memories[memories.length - 1]?.created_at,
        totalInteractions: conversationMessages.length,
        totalMemories: memories.length
      }
    })
  }

  // Format based on requested format
  if (format === 'text') {
    return {
      content: formatAsText(exportData),
      mimeType: 'text/plain',
      messageCount: conversationMessages.length
    }
  }

  return exportData
}

/**
 * Format export data as plain text
 */
function formatAsText(data: any): string {
  let text = `Chat History Export\n`
  text += `===================\n\n`
  text += `Document: ${data.document.name}\n`
  text += `Exported: ${new Date().toLocaleString()}\n`
  text += `Total Messages: ${data.messages.length}\n\n`
  
  text += `Conversation History:\n`
  text += `=====================\n\n`
  
  data.messages.forEach((msg: any) => {
    text += `[${new Date(msg.timestamp).toLocaleString()}] ${msg.role.toUpperCase()}:\n`
    text += `${msg.content}\n\n`
    text += `---\n\n`
  })
  
  return text
}// E
