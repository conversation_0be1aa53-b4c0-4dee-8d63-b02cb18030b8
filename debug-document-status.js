/**
 * Debug Document Status
 * Check if documents are properly processed and AI initialized
 */

// This will help us diagnose the issue
console.log('🔍 Document Status Checker')
console.log('After uploading a file, check:')
console.log('1. Go to your browser console')
console.log('2. Run this in the console:')
console.log('')
console.log('fetch("/api/documents").then(r => r.json()).then(console.log)')
console.log('')
console.log('This will show you all your documents and their status')
console.log('')
console.log('Look for:')
console.log('- status: should be "READY"')
console.log('- totalChunks: should be > 0')
console.log('')
console.log('If status is not READY, the AI cannot be initialized')