"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, DialogTitle, DialogDescription } from "@/components/ui/dialog"
import { DocumentUpload } from "./document-upload"

interface DocumentUploadModalProps {
  isOpen: boolean
  onClose: () => void
  onUploadComplete: () => void
}

export function DocumentUploadModal({ isOpen, onClose, onUploadComplete }: DocumentUploadModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] bg-white text-card-foreground border border-border shadow-sm">
        <DialogHeader>
          <DialogTitle>Upload New Document</DialogTitle>
          <DialogDescription className="text-muted-foreground">
            Upload your PDF or image files to start a new study session.
          </DialogDescription>
        </DialogHeader>
        <DocumentUpload onUploadComplete={onUploadComplete} />
      </DialogContent>
    </Dialog>
  )
}
