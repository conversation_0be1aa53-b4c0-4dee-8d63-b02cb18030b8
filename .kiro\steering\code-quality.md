# Code Quality Standards

## TypeScript Validation Requirements

**CRITICAL**: After creating or modifying ANY file, you MUST perform these checks:

### 1. TypeScript Check
- Run TypeScript validation on every file
- Fix all TypeScript errors before proceeding
- Ensure proper type safety

### 2. Unused Imports/Variables Check
- Check for unused imports (hover over imports to see "declared but never used")
- Check for unused variables and functions
- Remove ALL unused declarations
- Only import what is actually used in the code

### 3. Pre-Submission Checklist
Before considering any file complete:
- [ ] TypeScript compiles without errors
- [ ] No unused imports
- [ ] No unused variables or functions
- [ ] All declared items are actually used

### 4. Common Issues to Avoid
- Importing functions/components that aren't used
- Declaring variables that are never referenced
- Importing entire libraries when only specific functions are needed
- Leaving debug imports or temporary variables

## Implementation Rule
**DO NOT SKIP**: These checks are mandatory for every file modification. Failure to perform these checks causes technical debt and potential runtime issues.