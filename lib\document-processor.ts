import { dbService } from "./db-service"
import { prisma } from "./prisma"
import { llamaParseService, type LlamaParseDocument } from "./llamaparse-service"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "./error-handler"

/**
 * Background document processing function
 * Extracted from upload route to avoid circular dependencies with job queue
 */
export async function processDocumentInBackground(
  documentId: string,
  buffer: Buffer,
  fileName: string,
  userId: string
): Promise<void> {
  console.log(`[${documentId}] Starting background processing for ${fileName} (${buffer.length} bytes)`)

  try {
    // Check environment variables first
    if (!process.env.LLAMA_CLOUD_API_KEY) {
      throw new Error('LLAMA_CLOUD_API_KEY environment variable is not configured')
    }

    console.log(`[${documentId}] Environment check passed, starting LlamaParse processing...`)
    // Add timeout to prevent hanging
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(() => reject(new Error('LlamaParse processing timed out after 5 minutes')), 5 * 60 * 1000)
    })

    // Parse document with timeout
    const parsedDocuments = await Promise.race([
      llamaParseService.parseDocumentFromBuffer(buffer, fileName),
      timeoutPromise
    ]) as LlamaParseDocument[]

    if (!parsedDocuments || parsedDocuments.length === 0) {
      console.log(`[${documentId}] No content extracted from document`)
      await dbService.document.update({
        where: { id: documentId },
        data: {
          status: 'ERROR',
          errorMessage: 'No content could be extracted from the document'
        }
      })
      return
    }

    console.log(`[${documentId}] Document successfully parsed into ${parsedDocuments.length} chunks`)

    // Use database transaction for consistency and bulk operations for performance
    console.log(`[${documentId}] Storing chunks in database using transaction...`)
    await prisma.$transaction(async (tx: any) => {
      // Clear existing chunks
      await tx.chunk.deleteMany({ where: { documentId } })

      // Prepare chunk data for bulk creation
      const chunkData = parsedDocuments.map((chunk, i) => ({
        documentId,
        chunkIndex: i,
        pageNumber: chunk.metadata.page_number || null,
        content: chunk.text,
        userId: userId
      }))

      // Bulk create chunks
      await tx.chunk.createMany({ data: chunkData })

      // Update document status
      await tx.document.update({
        where: { id: documentId },
        data: {
          status: 'READY',
          totalChunks: parsedDocuments.length,
          errorMessage: null
        }
      })
    })

    console.log(`[${documentId}] Successfully processed document ${documentId}`)

    // Document processing complete - AI will be initialized when user opens chat interface

  } catch (error) {
    console.error(`[${documentId}] Background processing failed:`, error)

    // Use comprehensive error handling
    await ErrorHandler.handleDocumentError(documentId, error, userId)
  }
}


