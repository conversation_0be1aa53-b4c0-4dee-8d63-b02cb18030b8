import { NextRequest } from 'next/server'

export interface RateLimitConfig {
  windowMs: number // Time window in milliseconds
  maxRequests: number // Maximum requests per window
  keyGenerator?: (req: NextRequest) => string // Custom key generator
  userId?: string // Optional user ID for authenticated rate limiting
}

export interface RateLimitStore {
  get(key: string): Promise<{ count: number; resetTime: number } | null>
  set(key: string, value: { count: number; resetTime: number }): Promise<void>
  delete(key: string): Promise<void>
  cleanup?(now: number): Promise<void>
}

class InMemoryRateLimitStore implements RateLimitStore {
  private store = new Map<string, { count: number; resetTime: number }>()

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    return this.store.get(key) || null
  }

  async set(key: string, value: { count: number; resetTime: number }): Promise<void> {
    this.store.set(key, value)
  }

  async delete(key: string): Promise<void> {
    this.store.delete(key)
  }

  async cleanup(now: number): Promise<void> {
    this.store.forEach((entry, key) => {
      if (now > entry.resetTime) {
        this.store.delete(key)
      }
    })
  }
}

// Redis implementation (optional - requires redis package)
class RedisRateLimitStore implements RateLimitStore {
  private redis: any

  constructor(redisClient: any) {
    this.redis = redisClient
  }

  async get(key: string): Promise<{ count: number; resetTime: number } | null> {
    try {
      const data = await this.redis.get(`ratelimit:${key}`)
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('Redis rate limit get error:', error)
      return null
    }
  }

  async set(key: string, value: { count: number; resetTime: number }): Promise<void> {
    try {
      const ttl = Math.max(1, Math.ceil((value.resetTime - Date.now()) / 1000))
      await this.redis.setex(`ratelimit:${key}`, ttl, JSON.stringify(value))
    } catch (error) {
      console.error('Redis rate limit set error:', error)
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(`ratelimit:${key}`)
    } catch (error) {
      console.error('Redis rate limit delete error:', error)
    }
  }

  // Redis handles TTL automatically, so cleanup is not needed
  async cleanup(): Promise<void> {
    // No-op for Redis
  }
}

export interface FileValidationResult {
  isValid: boolean
  error?: string
  fileType?: string
  size?: number
}

export class SecurityService {
  private static rateLimitStore: RateLimitStore = new InMemoryRateLimitStore()

  /**
   * Configure rate limit store (for production use with Redis)
   */
  static configureRateLimitStore(store: RateLimitStore): void {
    this.rateLimitStore = store
  }

  /**
   * Rate limiting middleware with multi-layer protection
   */
  static async checkRateLimit(req: NextRequest, config: RateLimitConfig): Promise<{ allowed: boolean; error?: string }> {
    const now = Date.now()

    // Clean up expired entries (only for in-memory store)
    if (this.rateLimitStore.cleanup) {
      await this.rateLimitStore.cleanup(now)
    }

    // Check IP-based rate limit first
    const ipKey = config.keyGenerator ? config.keyGenerator(req) : this.getDefaultKey(req)
    const ipResult = await this.checkSingleRateLimit(ipKey, config, now)

    if (!ipResult.allowed) {
      return ipResult
    }

    // If user ID is provided, also check user-based rate limit
    if (config.userId) {
      const userKey = `user:${config.userId}`
      const userResult = await this.checkSingleRateLimit(userKey, config, now)

      if (!userResult.allowed) {
        return {
          allowed: false,
          error: `User rate limit exceeded. Maximum ${config.maxRequests} requests per ${config.windowMs / 1000} seconds.`
        }
      }
    }

    return { allowed: true }
  }

  /**
   * Check rate limit for a single key
   */
  private static async checkSingleRateLimit(
    key: string,
    config: RateLimitConfig,
    now: number
  ): Promise<{ allowed: boolean; error?: string }> {
    const entry = await this.rateLimitStore.get(key)

    if (!entry) {
      // First request from this key
      await this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      return { allowed: true }
    }

    if (now > entry.resetTime) {
      // Window has expired, reset
      await this.rateLimitStore.set(key, {
        count: 1,
        resetTime: now + config.windowMs
      })
      return { allowed: true }
    }

    if (entry.count >= config.maxRequests) {
      // Rate limit exceeded
      return {
        allowed: false,
        error: `Rate limit exceeded. Maximum ${config.maxRequests} requests per ${config.windowMs / 1000} seconds.`
      }
    }

    // Increment count
    await this.rateLimitStore.set(key, {
      count: entry.count + 1,
      resetTime: entry.resetTime
    })
    return { allowed: true }
  }

  /**
   * Generate default rate limit key from request
   */
  private static getDefaultKey(req: NextRequest): string {
    // Get the most reliable IP address
    const ip = this.getTrustedIP(req)
    const userAgent = req.headers.get('user-agent') || 'unknown'
    return `${ip}:${userAgent.substring(0, 50)}`
  }

  /**
   * Get trusted IP address with validation against proxy spoofing
   */
  private static getTrustedIP(req: NextRequest): string {
    // Try to get the real IP from headers
    const forwardedFor = req.headers.get('x-forwarded-for')
    const realIP = req.headers.get('x-real-ip')
    const cfConnectingIP = req.headers.get('cf-connecting-ip') // Cloudflare

    // If we have forwarded headers, validate them
    if (forwardedFor) {
      // x-forwarded-for can contain multiple IPs: "client, proxy1, proxy2"
      const ips = forwardedFor.split(',').map(ip => ip.trim())
      const clientIP = ips[0] // First IP should be the original client

      if (this.isValidIP(clientIP)) {
        return clientIP
      }
    }

    if (realIP && this.isValidIP(realIP)) {
      return realIP
    }

    if (cfConnectingIP && this.isValidIP(cfConnectingIP)) {
      return cfConnectingIP
    }

    // Fallback to a generic identifier if no valid IP is found
    return 'unknown-client'
  }

  /**
   * Validate IP address format
   */
  private static isValidIP(ip: string): boolean {
    // Basic IPv4 validation
    const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/
    // Basic IPv6 validation (simplified)
    const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/

    return ipv4Regex.test(ip) || ipv6Regex.test(ip)
  }



  /**
   * Validate file before processing
   */
  static async validateFile(file: File, fileExtension: string): Promise<FileValidationResult> {
    try {
      // Check file size
      const maxSize = 50 * 1024 * 1024 // 50MB
      if (file.size > maxSize) {
        return {
          isValid: false,
          error: 'File size exceeds maximum limit of 50MB',
          size: file.size
        }
      }

      if (file.size === 0) {
        return {
          isValid: false,
          error: 'File is empty',
          size: file.size
        }
      }

      // Validate file type by extension and MIME type
      const mimeType = file.type

      const validationResult = this.validateFileType(fileExtension, mimeType)
      if (!validationResult.isValid) {
        return validationResult
      }

      // Read file header to validate actual file type
      const headerValidation = await this.validateFileHeader(file, fileExtension)
      if (!headerValidation.isValid) {
        return headerValidation
      }

      // Additional content validation
      const contentValidation = await this.validateFileContentInternal(file, fileExtension)
      if (!contentValidation.isValid) {
        return contentValidation
      }

      return {
        isValid: true,
        fileType: fileExtension,
        size: file.size
      }

    } catch (error) {
      return {
        isValid: false,
        error: `File validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Validate file type by extension and MIME type
   */
  private static validateFileType(extension: string, mimeType: string): FileValidationResult {
    const supportedTypes = {
      '.pdf': ['application/pdf'],
      '.docx': ['application/vnd.openxmlformats-officedocument.wordprocessingml.document'],
      '.doc': ['application/msword'],
      '.pptx': ['application/vnd.openxmlformats-officedocument.presentationml.presentation'],
      '.ppt': ['application/vnd.ms-powerpoint'],
      '.txt': ['text/plain'],
      '.md': ['text/markdown', 'text/plain'],
      '.rtf': ['application/rtf', 'text/rtf'],
      '.html': ['text/html'],
      '.htm': ['text/html']
    }

    if (!supportedTypes[extension as keyof typeof supportedTypes]) {
      return {
        isValid: false,
        error: `Unsupported file type: ${extension}. Supported types: ${Object.keys(supportedTypes).join(', ')}`
      }
    }

    const validMimeTypes = supportedTypes[extension as keyof typeof supportedTypes]
    if (mimeType && !validMimeTypes.includes(mimeType)) {
      // Allow if MIME type is not set or is generic
      if (mimeType !== 'application/octet-stream' && mimeType !== '') {
        console.warn(`MIME type mismatch for ${extension}: expected ${validMimeTypes.join(' or ')}, got ${mimeType}`)
      }
    }

    return { isValid: true, fileType: extension }
  }

  /**
   * Validate file header (magic bytes)
   */
  private static async validateFileHeader(file: File, expectedExtension: string): Promise<FileValidationResult> {
    try {
      // Read first 512 bytes for comprehensive validation
      const buffer = await file.slice(0, 512).arrayBuffer()
      const bytes = new Uint8Array(buffer)

      const magicNumbers = {
        '.pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
        '.doc': [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1], // OLE2 signature for .doc files
        '.rtf': [0x7B, 0x5C, 0x72, 0x74, 0x66], // {\rtf
      }

      // Handle ZIP-based formats (DOCX, PPTX) with content type validation
      if (expectedExtension === '.docx' || expectedExtension === '.pptx') {
        return await this.validateZipBasedOfficeFormat(file, expectedExtension)
      }

      const expectedMagic = magicNumbers[expectedExtension as keyof typeof magicNumbers]
      if (expectedMagic) {
        const matches = expectedMagic.every((byte, index) => bytes[index] === byte)
        if (!matches) {
          return {
            isValid: false,
            error: `File header does not match expected format for ${expectedExtension}`
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      // If we can't read the header, allow it but log the issue
      console.warn('Could not validate file header:', error)
      return { isValid: true }
    }
  }

  /**
   * Validate ZIP-based Office formats by checking internal structure
   */
  private static async validateZipBasedOfficeFormat(file: File, expectedExtension: string): Promise<FileValidationResult> {
    try {
      // First check if it's a ZIP file
      const buffer = await file.slice(0, 4).arrayBuffer()
      const bytes = new Uint8Array(buffer)
      const zipSignature = [0x50, 0x4B, 0x03, 0x04] // ZIP signature

      const isZip = zipSignature.every((byte, index) => bytes[index] === byte)
      if (!isZip) {
        return {
          isValid: false,
          error: `File is not a valid ZIP archive (required for ${expectedExtension})`
        }
      }

      // For more robust validation, we would need to parse the ZIP structure
      // This is a simplified check - in production, consider using a ZIP parsing library
      const fullBuffer = await file.arrayBuffer()
      const fullBytes = new Uint8Array(fullBuffer)
      const content = new TextDecoder('utf-8', { fatal: false }).decode(fullBytes)

      // Check for Office-specific content markers
      const officeMarkers = {
        '.docx': [
          'word/document.xml',
          'word/_rels/',
          '[Content_Types].xml'
        ],
        '.pptx': [
          'ppt/presentation.xml',
          'ppt/_rels/',
          '[Content_Types].xml'
        ]
      }

      const expectedMarkers = officeMarkers[expectedExtension as keyof typeof officeMarkers]
      if (expectedMarkers) {
        const hasRequiredMarkers = expectedMarkers.some(marker => content.includes(marker))
        if (!hasRequiredMarkers) {
          return {
            isValid: false,
            error: `File does not contain expected ${expectedExtension} structure`
          }
        }
      }

      return { isValid: true }
    } catch (error) {
      console.warn(`Could not validate ${expectedExtension} structure:`, error)
      // Fallback to basic ZIP validation
      const buffer = await file.slice(0, 4).arrayBuffer()
      const bytes = new Uint8Array(buffer)
      const zipSignature = [0x50, 0x4B, 0x03, 0x04]

      const isZip = zipSignature.every((byte, index) => bytes[index] === byte)
      if (!isZip) {
        return {
          isValid: false,
          error: `File is not a valid ZIP archive (required for ${expectedExtension})`
        }
      }

      return { isValid: true }
    }
  }


  /**
   * Additional content validation with performance optimizations
   */
  private static async validateFileContentInternal(file: File, extension: string): Promise<FileValidationResult> {
    try {
      // For text-based files, check for suspicious content
      if (['.txt', '.md', '.html', '.htm'].includes(extension)) {
        return await this.validateTextFileContent(file)
      }

      return { isValid: true }
    } catch (error) {
      return {
        isValid: false,
        error: `Content validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Validate text file content with streaming and performance optimizations
   */
  private static async validateTextFileContent(file: File): Promise<FileValidationResult> {
    const MAX_SCAN_SIZE = 1024 * 1024 // 1MB limit for regex scanning
    const REGEX_TIMEOUT = 5000 // 5 second timeout for regex operations

    try {
      // Check if file is empty
      if (file.size === 0) {
        return {
          isValid: false,
          error: 'Text file is empty'
        }
      }

      // For large files, only scan the first portion to avoid performance issues
      const scanSize = Math.min(file.size, MAX_SCAN_SIZE)
      const scanBuffer = await file.slice(0, scanSize).arrayBuffer()
      const scanText = new TextDecoder('utf-8', { fatal: false }).decode(scanBuffer)

      // Quick binary content check first (most efficient)
      const binaryCheckResult = this.checkBinaryContent(scanText)
      if (!binaryCheckResult.isValid) {
        return binaryCheckResult
      }

      // Perform malicious content detection with timeout protection
      const maliciousContentResult = await this.checkMaliciousContentWithTimeout(scanText, REGEX_TIMEOUT)
      if (!maliciousContentResult.isValid) {
        return maliciousContentResult
      }

      // If we only scanned part of the file, warn about partial validation
      if (scanSize < file.size) {
        console.warn(`Large file detected (${file.size} bytes). Only scanned first ${scanSize} bytes for security patterns.`)
      }

      return { isValid: true }
    } catch (error) {
      return {
        isValid: false,
        error: `Text content validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      }
    }
  }

  /**
   * Check for binary content in text files (fast, non-regex approach)
   */
  private static checkBinaryContent(text: string): FileValidationResult {
    let binaryCount = 0
    const sampleSize = Math.min(text.length, 10000) // Sample first 10KB for performance

    for (let i = 0; i < sampleSize; i++) {
      const charCode = text.charCodeAt(i)
      // Check for control characters that shouldn't be in text files
      if ((charCode >= 0 && charCode <= 8) ||
        (charCode >= 14 && charCode <= 31) ||
        (charCode >= 127 && charCode <= 255)) {
        binaryCount++
      }
    }

    // If more than 10% of sampled content is binary, reject
    if (binaryCount > sampleSize * 0.1) {
      return {
        isValid: false,
        error: 'Text file contains too much binary content'
      }
    }

    return { isValid: true }
  }

  /**
   * Check for malicious content with timeout protection against ReDoS
   */
  private static async checkMaliciousContentWithTimeout(text: string, timeoutMs: number): Promise<FileValidationResult> {
    // Simplified, safer patterns that avoid catastrophic backtracking
    const suspiciousPatterns = [
      {
        pattern: /<script[\s\S]*?<\/script>/gi,
        name: 'script tags'
      },
      {
        pattern: /javascript:/gi,
        name: 'javascript protocol'
      },
      {
        pattern: /vbscript:/gi,
        name: 'vbscript protocol'
      },
      {
        pattern: /onload\s*=/gi,
        name: 'onload event handler'
      },
      {
        pattern: /onerror\s*=/gi,
        name: 'onerror event handler'
      },
      {
        pattern: /onclick\s*=/gi,
        name: 'onclick event handler'
      },
      {
        pattern: /onmouseover\s*=/gi,
        name: 'onmouseover event handler'
      }
    ]

    return new Promise((resolve) => {
      const timeout = setTimeout(() => {
        resolve({
          isValid: false,
          error: 'Content validation timed out - file may contain complex patterns'
        })
      }, timeoutMs)

      try {
        // Use string search for common patterns first (faster than regex)
        const dangerousStrings = ['<script', 'javascript:', 'vbscript:', 'onload=', 'onerror=', 'onclick=']

        for (const dangerousString of dangerousStrings) {
          if (text.toLowerCase().includes(dangerousString)) {
            clearTimeout(timeout)
            resolve({
              isValid: false,
              error: `File contains potentially malicious content: ${dangerousString}`
            })
            return
          }
        }

        // If basic string search passes, do more thorough regex validation on smaller chunks
        const chunkSize = 50000 // 50KB chunks
        for (let i = 0; i < text.length; i += chunkSize) {
          const chunk = text.slice(i, i + chunkSize)

          for (const { pattern, name } of suspiciousPatterns) {
            try {
              if (pattern.test(chunk)) {
                clearTimeout(timeout)
                resolve({
                  isValid: false,
                  error: `File contains potentially malicious content: ${name}`
                })
                return
              }
            } catch (regexError) {
              // If regex fails, skip this pattern and continue
              console.warn(`Regex pattern failed for ${name}:`, regexError)
              continue
            }
          }
        }

        clearTimeout(timeout)
        resolve({ isValid: true })
      } catch (error) {
        clearTimeout(timeout)
        resolve({
          isValid: false,
          error: `Content validation error: ${error instanceof Error ? error.message : 'Unknown error'}`
        })
      }
    })
  }

  /**
   * Validate user authorization for document operations
   */
  static validateDocumentAccess(documentUserId: string, requestUserId: string): boolean {
    return documentUserId === requestUserId
  }

  /**
   * Sanitize user input
   */
  static sanitizeInput(input: string): string {
    return input
      .trim()
      .replace(/[&<>"']/g, (char) => {
        const escapeMap: Record<string, string> = {
          '&': '&amp;',
          '<': '&lt;',
          '>': '&gt;',
          '"': '&quot;',
          "'": '&#39;'
        }
        return escapeMap[char]
      })
      .substring(0, 1000)
  }

  /**
   * Generate secure session ID
   */
  static generateSecureSessionId(): string {
    if (typeof crypto !== 'undefined' && crypto.randomUUID) {
      return `session_${crypto.randomUUID()}`
    }
    // Fallback for environments without crypto.randomUUID
    const array = new Uint8Array(16)
    crypto.getRandomValues(array)
    const hex = Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
    return `session_${hex}`
  }

  /**
   * Rate limit configurations for different endpoints
   */
  static readonly RATE_LIMITS = {
    UPLOAD: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 5 // 5 uploads per minute
    },
    PROCESSING: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 10 // 10 processing requests per minute
    },
    CHAT: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 30 // 30 chat messages per minute
    },
    GENERAL: {
      windowMs: 60 * 1000, // 1 minute
      maxRequests: 100 // 100 general requests per minute
    }
  }
}

/**
 * Rate limiting middleware function
 */
export function withRateLimit(config: RateLimitConfig) {
  return async (req: NextRequest) => {
    return await SecurityService.checkRateLimit(req, config)
  }
}

/**
 * Rate limiting middleware function with user authentication
 */
export function withAuthenticatedRateLimit(config: RateLimitConfig, userId: string) {
  return async (req: NextRequest) => {
    return await SecurityService.checkRateLimit(req, { ...config, userId })
  }
}

// Export store classes for external configuration
export { InMemoryRateLimitStore, RedisRateLimitStore }

/**
 * File validation middleware function
 */
export async function withFileValidation(file: File): Promise<FileValidationResult> {
  const fileName = file.name.toLowerCase()
  const lastDotIndex = fileName.lastIndexOf('.')
  const fileExtension = lastDotIndex > 0 ? fileName.substring(lastDotIndex) : ''

  if (!fileExtension) {
    return {
      isValid: false,
      error: 'File must have an extension'
    }
  }

  return SecurityService.validateFile(file, fileExtension)
}
