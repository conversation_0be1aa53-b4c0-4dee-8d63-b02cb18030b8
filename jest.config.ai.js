/**
 * Jest Configuration for AI Chat Integration Tests
 */

export default {
  displayName: 'AI Chat Integration Tests',
  testMatch: ['<rootDir>/lib/ai/__tests__/**/*.test.ts'],
  preset: 'ts-jest',
  testEnvironment: 'node',
  
  // Setup files
  setupFilesAfterEnv: ['<rootDir>/lib/ai/__tests__/setup.ts'],
  
  // Module resolution
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },
  
  // Coverage configuration
  collectCoverage: true,
  collectCoverageFrom: [
    'lib/ai/**/*.ts',
    '!lib/ai/__tests__/**',
    '!lib/ai/**/*.d.ts',
    '!lib/ai/**/index.ts'
  ],
  coverageDirectory: 'coverage/ai-integration',
  coverageReporters: ['text', 'lcov', 'html'],
  // Temporarily disable coverage thresholds while fixing tests
  // coverageThreshold: {
  //   global: {
  //     branches: 75,
  //     functions: 80,
  //     lines: 80,
  //     statements: 80
  //   }
  // },
  
  // Test timeout
  testTimeout: 15000,
  
  // Mock configuration
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,
  
  // Transform configuration
  transform: {
    '^.+\\.ts$': 'ts-jest'
  },
  
  // Module file extensions
  moduleFileExtensions: ['ts', 'js', 'json'],
  
  // Verbose output
  verbose: true,
  
  // Error handling
  bail: false,
  forceExit: true,
  
  // Parallel execution
  maxWorkers: '50%',
  
  // Test result processor - disabled for now
  // testResultsProcessor: '<rootDir>/lib/ai/__tests__/test-results-processor.js'
}