import { NextResponse } from 'next/server'
import { testAIProviders, validateAIConfig, getAIConfig } from '@/lib/ai'

export async function GET() {
  try {
    const config = getAIConfig()
    const validation = validateAIConfig()
    
    // Test the providers
    const testResult = await testAIProviders()
    
    return NextResponse.json({
      success: true,
      config: {
        provider: config.provider,
        openaiConfigured: !!config.openai.apiKey,
        anthropicConfigured: !!config.anthropic.apiKey,
        mem0Configured: !!config.mem0.apiKey
      },
      validation,
      testResult
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : 'An unknown error occurred'
    }, { status: 500 })
  }
}
