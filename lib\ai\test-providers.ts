import { AIProviderFactory } from './providers'
import { validateAIConfig, logAIConfig } from './config'
import { memoryService } from './memory-service'

export async function testAIProviders() {
    console.log('🧪 Testing AI Provider Setup...\n')

    // Log current configuration
    logAIConfig()
    console.log('')

    // Validate configuration
    const validation = validateAIConfig()
    if (!validation.isValid) {
        console.log('❌ Cannot test providers due to configuration errors')
        return false
    }

    try {
        // Test provider creation
        const provider = AIProviderFactory.createProvider()
        console.log(`✅ Successfully created ${provider.name} provider`)

        // Test provider initialization
        await provider.initialize()
        console.log(`✅ Successfully initialized ${provider.name} provider`)

        // Test Mem0 memory service
        console.log('🧠 Testing Mem0 memory service...')
        const memoryConnected = await memoryService.testConnection()
        if (memoryConnected) {
            console.log('✅ Successfully connected to Mem0 service')
        } else {
            console.log('⚠️  Mem0 connection failed (check MEM0_API_KEY)')
        }

        // Test basic response generation (optional - requires API keys)
        if (process.env.NODE_ENV !== 'production') {
            console.log('🔄 Testing basic response generation...')

            const testContext = {
                userId: 'test-user',
                documentId: 'test-doc',
                chunkIndex: 0,
                chunkContent: 'This is a test chunk about mathematics.',
                sessionId: 'test-session',
                currentQuery: 'What is this content about?'
            }

            const responseGenerator = provider.generateResponse('What is this content about?', testContext)

            let responseReceived = false
            let tokenCount = 0

            for await (const _ of responseGenerator) {
                responseReceived = true
                tokenCount++
                if (tokenCount === 1) {
                    console.log('✅ Successfully received streaming response')
                    break // Don't consume the entire response in test
                }
            }

            if (responseReceived) {
                console.log('✅ AI provider is working correctly')
            } else {
                console.log('⚠️  No response received (check API keys)')
            }
        }

        return true
    } catch (error) {
        console.log(`❌ Error testing AI provider: ${error instanceof Error ? error.message : 'Unknown error'}`)
        return false
    }
}

// Export for use in API routes or scripts
export default testAIProviders