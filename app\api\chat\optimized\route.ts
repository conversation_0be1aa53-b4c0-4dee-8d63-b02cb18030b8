import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"
import { cacheService, CacheService } from "@/lib/cache-service"

/**
 * Optimized chat API endpoint
 * Uses caching for better performance
 */
export async function POST(request: NextRequest) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { documentId, message, chunkIndex = 0 } = body

    // Validate chunkIndex is a non-negative integer
    if (typeof chunkIndex !== 'number' || chunkIndex < 0 || !Number.isInteger(chunkIndex)) {
      return NextResponse.json({ error: "Invalid chunk index" }, { status: 400 })
    }

    if (!documentId) {
      return NextResponse.json({ error: "Document ID is required" }, { status: 400 })
    }

    // Validate documentId is a non-empty string
    if (typeof documentId !== 'string' || documentId.trim().length === 0) {
      return NextResponse.json({ error: "Invalid document ID" }, { status: 400 })
    }

    if (!message) {
      return NextResponse.json({ error: "Message is required" }, { status: 400 })
    }

    // Validate message is a non-empty string
    if (typeof message !== 'string' || message.trim().length === 0) {
      return NextResponse.json({ error: "Invalid message" }, { status: 400 })
    }
    // Generate cache key for this chat session
    const cacheKey = CacheService.generateChatKey(session.user.id, documentId)

    // Check if document exists and is ready
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id,
        status: 'READY'
      }
    })

    if (!document) {
      return NextResponse.json({ error: "Document not found or not ready" }, { status: 404 })
    }

    // Get or create progress record
    let progress = await dbService.progress.findUnique({
      where: {
        userId_documentId: {
          userId: session.user.id,
          documentId
        }
      }
    })

    if (!progress) {
      progress = await dbService.progress.create({
        data: {
          userId: session.user.id,
          documentId,
          currentChunk: 0, // Start from beginning or use Math.max(prevChunk, chunkIndex) when resuming
          sessionId: `session_${session.user.id}_${documentId}_${Date.now()}_${Math.random()
            .toString(36)
            .substring(2, 11)}`
        }
      })
    }
    // Get cached chat or initialize new one
    let chatCache = cacheService.getChat(cacheKey)
    if (!chatCache) {
      chatCache = {
        messages: [],
        currentChunk: progress.currentChunk,
        lastAccessed: Date.now()
      }
    }

    // Add user message to chat
    chatCache.messages.push({
      role: 'user',
      content: message,
      timestamp: new Date().toISOString()
    })

    // Get document chunk from cache or database
    let chunk
    const documentCache = cacheService.getDocument(documentId)

    // Look for cached chunk by chunkIndex
    const cachedChunk = documentCache?.chunks.find(c => c.chunkIndex === chunkIndex)
    
    if (cachedChunk) {
      // Use cached chunk
      chunk = cachedChunk
      console.log(`Using cached chunk ${chunkIndex} for document ${documentId}`)
    } else {
      // Get chunk from database
      chunk = await dbService.chunk.findUnique({
        where: {
          documentId_chunkIndex: {
            documentId,
            chunkIndex
          }
        }
      })

      if (!chunk) {
        return NextResponse.json({ error: "Chunk not found" }, { status: 404 })
      }

      // Update document cache with retrieved chunk
      if (documentCache) {
        // Add the chunk to the existing chunks array
        const chunkData = {
          chunkIndex: chunk.chunkIndex,
          content: chunk.content,
          pageNumber: chunk.pageNumber
        }
        
        // Check if chunk already exists in cache
        const existingChunkIndex = documentCache.chunks.findIndex(c => c.chunkIndex === chunkIndex)
        
        if (existingChunkIndex >= 0) {
          // Update existing chunk
          documentCache.chunks[existingChunkIndex] = chunkData
        } else {
          // Add new chunk and sort by chunkIndex to maintain order
          documentCache.chunks.push(chunkData)
          documentCache.chunks.sort((a, b) => a.chunkIndex - b.chunkIndex)
        }
        
        cacheService.setDocument(documentId, documentCache)
        console.log(`Updated cache with chunk ${chunkIndex} for document ${documentId}`)
      }
    }

    // Generate AI response (simplified for this example)
    // In a real implementation, this would call an AI service
    const aiResponse = `This is a simulated response for chunk ${chunkIndex} of document ${documentId}. 
    The chunk content is about ${chunk.content.substring(0, 50)}...`;

    // Add AI response to chat
    chatCache.messages.push({
      role: 'assistant',
      content: aiResponse,
      timestamp: new Date().toISOString()
    })

    // Update cache
    cacheService.setChat(cacheKey, chatCache)

    return NextResponse.json({
      response: aiResponse,
      currentChunk: chunkIndex,
      totalChunks: document.totalChunks
    })

  } catch (error) {
    console.error("Chat error:", error)

    return NextResponse.json({
      error: "Failed to process chat message",
      details: error instanceof Error ? error.message : "Unknown error"
    }, { status: 500 })
  }
}
