/**
 * Jest Test Setup for AI Chat Integration Tests
 * Global setup and configuration for all AI tests
 */

// Jest globals are available globally, no need to import

// Global test setup
beforeAll(() => {
  // Set test environment variables
  ;(process.env as any).NODE_ENV = 'test'
  process.env.OPENAI_API_KEY = 'test-openai-key'
  process.env.ANTHROPIC_API_KEY = 'test-anthropic-key'
  process.env.MEM0_API_KEY = 'test-mem0-key'
  process.env.AI_PROVIDER = 'openai'
  
  // Mock console methods to reduce noise in tests
  global.console = {
    ...console,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    debug: jest.fn()
  }
})

// Global test teardown
afterAll(() => {
  // Clean up environment variables
  delete process.env.OPENAI_API_KEY
  delete process.env.ANTHROPIC_API_KEY
  delete process.env.MEM0_API_KEY
  delete process.env.AI_PROVIDER
})

// Global test configuration
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks()
  
  // Reset modules to ensure clean state
  jest.resetModules()
})

// Custom matchers for AI testing
expect.extend({
  toBeValidAIResponse(received: string) {
    const isString = typeof received === 'string'
    const hasContent = received && received.trim().length > 0
    const isReasonableLength = received.length > 10 && received.length < 5000
    
    const pass = isString && hasContent && isReasonableLength
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to be a valid AI response`,
        pass: true
      }
    } else {
      return {
        message: () => `Expected ${received} to be a valid AI response (string with reasonable length)`,
        pass: false
      }
    }
  },
  
  toHaveSocraticElements(received: string) {
    const hasQuestions = (received.match(/\?/g) || []).length > 0
    const hasInquiryWords = /what|how|why|can you|think about|consider/i.test(received)
    
    const pass = hasQuestions && hasInquiryWords
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to have Socratic elements`,
        pass: true
      }
    } else {
      return {
        message: () => `Expected ${received} to have Socratic elements (questions and inquiry words)`,
        pass: false
      }
    }
  },
  
  toBeEducationallyAppropriate(received: string) {
    const isEncouraging = /great|excellent|good|well done|fantastic|wonderful/i.test(received)
    const buildsOnKnowledge = /you know|you mentioned|remember|think about|consider/i.test(received)
    const isNotDirectAnswer = !/is the process|the answer is|simply put/i.test(received)
    
    const pass = isEncouraging || buildsOnKnowledge || isNotDirectAnswer
    
    if (pass) {
      return {
        message: () => `Expected ${received} not to be educationally appropriate`,
        pass: true
      }
    } else {
      return {
        message: () => `Expected ${received} to be educationally appropriate (encouraging, builds on knowledge, or not a direct answer)`,
        pass: false
      }
    }
  }
})

// Declare custom matchers for TypeScript
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace jest {
    interface Matchers<R> {
      toBeValidAIResponse(): R
      toHaveSocraticElements(): R
      toBeEducationallyAppropriate(): R
    }
  }
}

// Mock implementations for common dependencies
export const mockSupabaseClient = {
  auth: {
    getSession: jest.fn().mockResolvedValue({
      data: { session: { user: { id: 'test-user' } } }
    })
  }
}

export const mockDbService = {
  document: {
    findFirst: jest.fn().mockResolvedValue({
      id: 'test-doc',
      status: 'READY',
      totalChunks: 5
    })
  },
  chunk: {
    findUnique: jest.fn().mockResolvedValue({
      id: 'test-chunk',
      content: 'Test educational content'
    })
  },
  progress: {
    findUnique: jest.fn().mockResolvedValue({
      sessionId: 'test-session',
      currentChunk: 0
    }),
    upsert: jest.fn().mockResolvedValue({
      sessionId: 'test-session'
    })
  }
}

export const mockMemoryService = {
  initialize: jest.fn().mockResolvedValue(undefined),
  isAvailable: jest.fn().mockReturnValue(true),
  initializeSession: jest.fn().mockResolvedValue('test-session'),
  addConversationMessage: jest.fn().mockResolvedValue(undefined),
  getContextualMemory: jest.fn().mockResolvedValue([]),
  buildEducationalContext: jest.fn().mockResolvedValue('Test educational context'),
  getAllMemories: jest.fn().mockResolvedValue([]),
  testConnection: jest.fn().mockResolvedValue(true)
}

export const mockAIProvider = {
  name: 'test-provider' as const,
  initialize: jest.fn().mockResolvedValue(undefined),
  generateResponse: jest.fn().mockImplementation(async function* () {
    yield 'Test '
    yield 'AI '
    yield 'response'
  })
}

export const mockFallbackManager = {
  getCurrentProvider: jest.fn().mockReturnValue(mockAIProvider),
  getCurrentProviderName: jest.fn().mockReturnValue('test-provider'),
  executeWithFallback: jest.fn().mockResolvedValue('Test result'),
  generateStreamWithFallback: jest.fn().mockImplementation(async function* () {
    yield 'Test '
    yield 'streaming '
    yield 'response'
  }),
  getStatus: jest.fn().mockReturnValue({
    currentProvider: 'test-provider',
    availableProviders: ['test-provider'],
    failedProviders: [],
    hasFallback: false
  })
}

// Helper functions for tests
export function createMockRequest(body: any) {
  return {
    json: jest.fn().mockResolvedValue(body)
  } as any
}

export function createMockStreamingResponse() {
  const chunks: string[] = []
  return {
    chunks,
    mockStream: {
      async *[Symbol.asyncIterator]() {
        for (const chunk of chunks) {
          yield { choices: [{ delta: { content: chunk } }] }
        }
      }
    },
    addChunk: (chunk: string) => chunks.push(chunk)
  }
}

export async function collectStreamTokens(generator: AsyncGenerator<string>) {
  const tokens: string[] = []
  for await (const token of generator) {
    tokens.push(token)
  }
  return tokens
}

// Test utilities
export const testUtils = {
  createMockRequest,
  createMockStreamingResponse,
  collectStreamTokens,
  
  // Wait for async operations
  waitFor: (ms: number) => new Promise(resolve => setTimeout(resolve, ms)),
  
  // Generate test data
  generateTestEducationalContent: () => ({
    content: 'Photosynthesis is the process by which plants convert sunlight into energy.',
    topic: 'photosynthesis',
    difficulty: 'intermediate'
  }),
  
  generateTestConversation: () => [
    { role: 'user', content: 'What is photosynthesis?' },
    { role: 'assistant', content: 'Great question! What do you already know about how plants get energy?' },
    { role: 'user', content: 'Plants need sunlight to grow' }
  ]
}
