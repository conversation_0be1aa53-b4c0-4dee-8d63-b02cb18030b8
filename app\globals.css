@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 98%; /* Light neutral background for main area and sidebar */
    --foreground: 0 0% 9%; /* Very dark text like Opennote */

    --card: 0 0% 100%; /* Pure white for cards/panels within main area */
    --card-foreground: 0 0% 9%;

    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 9%;

    --primary: 168 76% 42%; /* Opennote's teal/green color */
    --primary-foreground: 0 0% 100%; /* White text on primary */

    --secondary: 0 0% 97%; /* Very light gray for backgrounds, slightly darker than main background */
    --secondary-foreground: 0 0% 9%;

    --muted: 0 0% 96%; /* Light gray for muted elements */
    --muted-foreground: 0 0% 45%; /* Medium gray for muted text */

    --accent: 0 0% 96%; /* Light gray for accents */
    --accent-foreground: 0 0% 9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 0 0% 88%; /* Light gray borders */
    --input: 0 0% 100%; /* Pure white input backgrounds */
    --ring: 168 76% 42%; /* Primary color for focus rings */

    --radius: 0.5rem;

    /* Sidebar specific colors - using exact color #F5F5F5 */
    --sidebar-background: 0 0% 96%; /* #F5F5F5 in HSL format */
    --sidebar-foreground: 0 0% 9%;
    --sidebar-primary: 168 76% 42%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 0 0% 96%; /* Consistent with main accent */
    --sidebar-accent-foreground: 0 0% 9%;
    --sidebar-border: 0 0% 88%;
    --sidebar-ring: 168 76% 42%;

    /* Font families using Poppins font */
    --font-heading: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
    --font-body: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
  }



  body {
    font-family: var(--font-poppins), ui-sans-serif, system-ui, sans-serif;
    font-weight: 400;
    line-height: 1.6;
    letter-spacing: -0.01em;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-weight: 600;
    line-height: 1.2;
    letter-spacing: -0.02em;
  }

  /* Grid background pattern */
  .grid-background {
    background-image: linear-gradient(rgba(0, 0, 0, 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
    position: relative;
  }

  /* Floating elements animation */
  .floating-element {
    animation: float 6s ease-in-out infinite;
  }

  .floating-element:nth-child(2) {
    animation-delay: -2s;
  }

  .floating-element:nth-child(3) {
    animation-delay: -4s;
  }

  @keyframes float {
    0%,
    100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-20px) rotate(5deg);
    }
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  .font-heading {
    font-family: var(--font-heading);
    font-weight: 600;
    letter-spacing: -0.02em;
  }

  .font-body {
    font-family: var(--font-body);
    font-weight: 400;
    letter-spacing: -0.01em;
  }

  .truncate-filename {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
