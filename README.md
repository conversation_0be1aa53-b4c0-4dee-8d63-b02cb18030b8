# Guided Tutor

An advanced educational platform built with Next.js and modern web technologies, designed to provide personalized learning experiences through AI-powered tutoring.

## 🚀 About

Guided Tutor is an innovative educational platform that combines AI technology with modern web development practices to create an engaging and effective learning environment. It offers personalized tutoring experiences, interactive features, and a modern, responsive user interface.

## 🛠️ Tech Stack

- **Frontend:**
  - Next.js 15.2.4
  - React 19
  - TypeScript
  - Tailwind CSS
  - Radix UI Components
  - React Hook Form
  - Zod for form validation

- **Backend:**
  - Supabase for authentication and database management

## 📱 Features

- **Modern Dashboard:**
  - Personalized learning interface
  - Progress tracking
  - Interactive features

- **Authentication System:**
  - Secure user registration and login
  - Role-based access control

- **AI Integration:**
  - Intelligent tutoring system
  - Personalized learning recommendations

- **Document Handling:**
  - File upload and management
  - Document processing capabilities

- **Chat/Messaging:**
  - Real-time communication
  - Interactive learning sessions

## 🚀 Getting Started

### Prerequisites

- Node.js (Latest LTS version)
- npm or yarn package manager
- Supabase account

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```
3. Copy `.env.example` to `.env` and update with your Supabase credentials
4. Run the development server:
   ```bash
   npm run dev
   # or
   yarn dev
   ```

## 📝 Project Structure

```
guided-tutor/
├── app/              # Next.js app router pages and routes
├── components/       # Reusable React components
├── lib/             # Utility functions and configurations
├── hooks/           # Custom React hooks
├── public/          # Static assets
└── styles/          # CSS files
```

## 🤝 Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- Next.js team for their amazing framework
- Supabase team for their excellent backend services
- All contributors who have helped shape this project
