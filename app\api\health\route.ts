import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'

/**
 * Health check endpoint to test Supabase connectivity
 */
export async function GET(_request: NextRequest) {
  try {
    const supabase = await createClient()
    
    // Simple auth check to test Supabase connection
    const { error } = await supabase.auth.getSession()
    
    if (error) {
      console.warn('Health check - auth error:', error.message)
    }
    
    return NextResponse.json({ 
      status: 'ok', 
      timestamp: new Date().toISOString(),
      supabase: error ? 'error' : 'connected'
    })
  } catch (error) {
    console.error('Health check failed:', error)
    return NextResponse.json({ 
      status: 'error', 
      timestamp: new Date().toISOString(),
      error: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}