# File Upload Feature Design

## Overview

The file upload feature is **partially implemented** with a solid foundation already in place. The system has dual upload entry points (main area CompactUpload and sidebar DocumentUploadModal), Supabase storage integration, and complete document management. **Missing components**: Llama Parser API integration, automatic chunk processing, and status tracking for processing states.

## Architecture

### Current Implementation Status
✅ **IMPLEMENTED**:
- Dashboard layout with main area and sidebar
- CompactUpload component (main area)
- DocumentUpload + DocumentUploadModal (sidebar)
- DocumentList component with status display
- File upload to Supabase Storage
- Document CRUD operations
- File validation (PDF, images, 50MB limit)
- Database models (Document, Chunk, Progress)

❌ **MISSING**:
- Llama Parser API integration
- Automatic chunk processing after upload
- Document status tracking (processing/ready/error)
- First chunk AI processing

### Current Flow (Implemented)
```mermaid
graph TD
    A[User clicks upload button] --> B[File selection dialog]
    B --> C[File validation]
    C --> D[Upload to Supabase Storage]
    D --> E[Store document metadata in DB]
    E --> F[Display file in sidebar]
    F --> G[File ready for manual selection]
```

### Target Flow (What We Need to Build)
```mermaid
graph TD
    A[Current: File uploaded & stored] --> B[NEW: Initialize LlamaParseReader]
    B --> C[NEW: Parse file with LlamaParse]
    C --> D[NEW: Receive structured documents/chunks]
    D --> E[NEW: Store chunks in database]
    E --> F[NEW: Update document status to 'READY']
    F --> G[NEW: Send first chunk to AI]
    G --> H[File ready for chat]
```

## Components and Interfaces

### Frontend Components

#### FileUploadButton Component
```typescript
interface FileUploadButtonProps {
  variant: 'main' | 'sidebar'
  onUploadStart: (file: File) => void
  onUploadComplete: (document: Document) => void
}
```

#### FileList Component (Sidebar)
```typescript
interface FileListProps {
  documents: Document[]
  onFileSelect: (documentId: string) => void
  onFileDelete: (documentId: string) => void
}
```

#### UploadProgress Component
```typescript
interface UploadProgressProps {
  fileName: string
  progress: number
  status: 'uploading' | 'processing' | 'complete' | 'error'
}
```

### API Endpoints

#### File Upload API
```typescript
// POST /api/upload
interface UploadRequest {
  file: File
  userId: string
}

interface UploadResponse {
  documentId: string
  fileName: string
  filePath: string
  status: 'uploaded' | 'processing'
}
```

#### File Processing API
```typescript
// POST /api/process-document
interface ProcessRequest {
  documentId: string
  filePath: string
}

interface ProcessResponse {
  success: boolean
  totalChunks: number
  status: 'processing' | 'complete' | 'error'
}
```

#### LlamaParse Integration (TypeScript)
```typescript
// Install: pnpm add llamaindex dotenv
import { LlamaParseReader } from 'llamaindex';

// Initialize LlamaParse Reader
const reader = new LlamaParseReader({
  resultType: 'markdown', // or 'text' or 'json'
  apiKey: process.env.LLAMA_CLOUD_API_KEY
});

// Parse document from file path or buffer
const documents = await reader.loadData(filePath);

// Document structure returned by LlamaParse
interface LlamaParseDocument {
  id_: string;
  text: string;
  metadata: {
    file_path: string;
    [key: string]: any;
  };
}
```

## Data Models

### Existing Models (No Changes Required)

The current Prisma schema already supports the file upload feature:

#### Document Model
```prisma
model Document {
  id          String     @id @default(cuid())
  fileName    String     @map("file_name")
  filePath    String     @map("file_path")
  totalChunks Int        @default(0) @map("total_chunks")
  createdAt   DateTime   @default(now()) @map("created_at")
  updatedAt   DateTime   @updatedAt @map("updated_at")
  userId      String     @db.Uuid
  chunks      Chunk[]
  user        User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  progress    Progress[]
}
```

#### Chunk Model
```prisma
model Chunk {
  id         String   @id @default(cuid())
  documentId String   @map("document_id")
  chunkIndex Int      @map("chunk_index")
  pageNumber Int?     @map("page_number")
  content    String
  createdAt  DateTime @default(now()) @map("created_at")
  updatedAt  DateTime @updatedAt @map("updated_at")
  userId     String?  @map("user_id") @db.Uuid
  document   Document @relation(fields: [documentId], references: [id], onDelete: Cascade)
  user       User?    @relation(fields: [userId], references: [id], onDelete: Cascade)
}
```

### Schema Updates Needed

Add processing status tracking to Document model:
```prisma
// Add to existing Document model
status       String   @default("PROCESSING") // "PROCESSING" | "READY" | "ERROR"
errorMessage String?  @map("error_message")
```

Note: The DocumentList component already expects these status values: "PROCESSING" | "READY" | "ERROR"

## File Storage Strategy

### Supabase Storage Integration
- **Bucket**: `documents` (user-specific folders)
- **Path Structure**: `{userId}/{documentId}/{fileName}`
- **Security**: Row Level Security (RLS) policies
- **File Types**: PDF, DOCX, TXT, MD (validated on upload)
- **Size Limit**: 10MB maximum

### Storage Policies
```sql
-- Allow users to upload to their own folder
CREATE POLICY "Users can upload own files" ON storage.objects
FOR INSERT WITH CHECK (auth.uid()::text = (storage.foldername(name))[1]);

-- Allow users to read their own files
CREATE POLICY "Users can read own files" ON storage.objects
FOR SELECT USING (auth.uid()::text = (storage.foldername(name))[1]);
```

## Error Handling

### File Validation Errors
- **Unsupported file type**: Display supported formats (PDF, DOCX, TXT, MD)
- **File too large**: Show 10MB limit message
- **Empty file**: Reject with appropriate message

### Upload Errors
- **Network failure**: Retry mechanism with exponential backoff
- **Storage quota exceeded**: Clear error message with upgrade options
- **Authentication errors**: Redirect to login

### Processing Errors
- **Llama Parser API failure**: Mark document as error, allow retry
- **Chunk storage failure**: Rollback document creation
- **Invalid file content**: Mark as error with descriptive message

### Error Recovery
- **Retry mechanism**: For transient failures
- **Cleanup on failure**: Remove partial uploads and database entries
- **User notification**: Toast messages for all error states

## Testing Strategy

### Unit Tests
- File validation logic
- Upload progress tracking
- Chunk processing functions
- Database operations (CRUD)

### Integration Tests
- Complete upload flow (file → storage → database)
- Llama Parser API integration
- Error handling scenarios
- File deletion and cleanup

### End-to-End Tests
- Upload from main area button
- Upload from sidebar button
- File appears in sidebar after upload
- Processing status updates
- Error state handling

### Performance Tests
- Large file upload (up to 10MB)
- Multiple concurrent uploads
- Chunk processing performance
- Database query optimization

## Security Considerations

### File Security
- **Virus scanning**: Consider integration with antivirus service
- **Content validation**: Verify file headers match extensions
- **Access control**: RLS policies for all file operations

### API Security
- **Rate limiting**: Prevent upload abuse
- **Authentication**: Verify user session for all operations
- **Input validation**: Sanitize all file metadata

### Data Privacy
- **Encryption**: Files encrypted at rest in Supabase Storage
- **Access logs**: Track file access for security auditing
- **Data retention**: Automatic cleanup of deleted files

## Performance Optimization

### Upload Performance
- **Chunked uploads**: For large files (future enhancement)
- **Compression**: Client-side compression before upload
- **CDN integration**: Faster file delivery

### Processing Performance
- **Background jobs**: Async processing with job queue
- **Caching**: Cache processed chunks for faster retrieval
- **Database indexing**: Optimize queries for file lists

### UI Performance
- **Optimistic updates**: Show file in sidebar immediately
- **Progressive loading**: Load file list incrementally
- **Real-time updates**: WebSocket for processing status