/**
 * AI Provider Fallback Manager
 * Manages fallback between different AI providers when primary fails
 */

import { AIProvider, OpenAIProvider, AnthropicProvider } from './providers'
import { AIErrorHandler, AIError, ErrorType } from './error-handler'

export interface FallbackConfig {
    primaryProvider: string
    fallbackProviders: string[]
    maxFallbackAttempts: number
    fallbackDelay: number
}

export class FallbackManager {
    private providers: Map<string, AIProvider> = new Map()
    private config: FallbackConfig
    private currentProvider: string
    private failedProviders: Set<string> = new Set()

    constructor(config?: Partial<FallbackConfig>) {
        this.config = {
            primaryProvider: process.env.AI_PROVIDER || 'openai',
            fallbackProviders: ['anthropic', 'openai'],
            maxFallbackAttempts: 2,
            fallbackDelay: 1000,
            ...config
        }

        this.currentProvider = this.config.primaryProvider
        this.initializeProviders()
    }

    private initializeProviders(): void {
        // Initialize OpenAI provider if API key is available
        if (process.env.OPENAI_API_KEY) {
            this.providers.set('openai', new OpenAIProvider())
        }

        // Initialize Anthropic provider if API key is available
        if (process.env.ANTHROPIC_API_KEY) {
            this.providers.set('anthropic', new AnthropicProvider())
        }

        console.log(`✅ Initialized ${this.providers.size} AI providers`)
        console.log(`🎯 Primary provider: ${this.currentProvider}`)
    }

    /**
     * Get the current active provider
     */
    getCurrentProvider(): AIProvider | null {
        return this.providers.get(this.currentProvider) || null
    }

    /**
     * Get provider name for display
     */
    getCurrentProviderName(): string {
        return this.currentProvider
    }

    /**
     * Check if fallback is available
     */
    hasFallbackAvailable(): boolean {
        const availableProviders = Array.from(this.providers.keys())
            .filter(name => !this.failedProviders.has(name))
        
        return availableProviders.length > 1 || 
               (availableProviders.length === 1 && !availableProviders.includes(this.currentProvider))
    }

    /**
     * Switch to next available provider
     */
    private switchToFallback(): boolean {
        const availableProviders = this.config.fallbackProviders
            .filter(name => 
                this.providers.has(name) && 
                !this.failedProviders.has(name) && 
                name !== this.currentProvider
            )

        if (availableProviders.length === 0) {
            console.error('❌ No fallback providers available')
            return false
        }

        const nextProvider = availableProviders[0]
        console.log(`🔄 Switching from ${this.currentProvider} to ${nextProvider}`)
        
        this.currentProvider = nextProvider
        return true
    }

    /**
     * Mark provider as failed temporarily
     */
    private markProviderFailed(providerName: string): void {
        this.failedProviders.add(providerName)
        console.log(`⚠️ Marked ${providerName} as failed`)

        // Reset failed providers after some time
        setTimeout(() => {
            this.failedProviders.delete(providerName)
            console.log(`✅ Reset failure status for ${providerName}`)
        }, 5 * 60 * 1000) // 5 minutes
    }

    /**
     * Execute AI request with automatic fallback
     */
    async executeWithFallback<T>(
        operation: (provider: AIProvider) => Promise<T>,
        context?: string
    ): Promise<T> {
        let attempts = 0
        let lastError: AIError | null = null

        while (attempts <= this.config.maxFallbackAttempts) {
            const provider = this.getCurrentProvider()
            
            if (!provider) {
                throw new Error('No AI providers available')
            }

            try {
                console.log(`🤖 Attempting ${context || 'operation'} with ${this.currentProvider}`)
                return await operation(provider)
                
            } catch (error) {
                const aiError = error instanceof Error && 'type' in error && 'retryable' in error && 'fallbackAvailable' in error
                    ? error as AIError 
                    : AIErrorHandler.classifyError(error, this.currentProvider)
                
                lastError = aiError
                AIErrorHandler.logError(aiError, context)

                // Mark current provider as failed for certain error types
                if (AIErrorHandler.shouldFallback(aiError)) {
                    this.markProviderFailed(this.currentProvider)
                    
                    // Try to switch to fallback
                    if (this.hasFallbackAvailable() && this.switchToFallback()) {
                        attempts++
                        
                        // Wait before trying fallback
                        if (this.config.fallbackDelay > 0) {
                            await new Promise(resolve => 
                                setTimeout(resolve, this.config.fallbackDelay)
                            )
                        }
                        continue
                    }
                }

                // If error is not fallback-worthy or no fallback available, throw immediately
                throw aiError
            }
        }

        // If we've exhausted all attempts, throw the last error
        throw lastError || new Error('All fallback attempts failed')
    }

    /**
     * Generate streaming response with fallback
     */
    async *generateStreamWithFallback(
        prompt: string,
        context: any
    ): AsyncGenerator<string, void, unknown> {
        let attempts = 0
        let lastError: AIError | null = null

        while (attempts <= this.config.maxFallbackAttempts) {
            const provider = this.getCurrentProvider()
            
            if (!provider) {
                console.error('❌ No AI providers available')
                throw new Error('No AI providers available')
            }

            try {
                console.log(`🌊 Starting stream with ${this.currentProvider} (attempt ${attempts + 1})`)
                
                let tokenCount = 0
                for await (const chunk of provider.generateResponse(prompt, context)) {
                    tokenCount++
                    yield chunk
                }
                
                console.log(`✅ Stream completed successfully with ${this.currentProvider}, tokens: ${tokenCount}`)
                return // Success, exit the retry loop
                
            } catch (error) {
                console.error(`❌ Stream failed with ${this.currentProvider}:`, error)
                
                const aiError = error instanceof Error && 'type' in error && 'retryable' in error && 'fallbackAvailable' in error
                    ? error as AIError 
                    : AIErrorHandler.classifyError(error, this.currentProvider)
                
                lastError = aiError
                AIErrorHandler.logError(aiError, 'streaming')

                // Mark current provider as failed for certain error types
                if (AIErrorHandler.shouldFallback(aiError)) {
                    this.markProviderFailed(this.currentProvider)
                    
                    // Try to switch to fallback
                    if (this.hasFallbackAvailable() && this.switchToFallback()) {
                        attempts++
                        
                        console.log(`🔄 Attempting fallback to ${this.currentProvider} after ${this.config.fallbackDelay}ms delay`)
                        
                        // Wait before trying fallback
                        if (this.config.fallbackDelay > 0) {
                            await new Promise(resolve => 
                                setTimeout(resolve, this.config.fallbackDelay)
                            )
                        }
                        continue
                    }
                }

                // If error is not fallback-worthy or no fallback available, throw immediately
                console.error(`❌ No fallback available or error not fallback-worthy, throwing error`)
                throw aiError
            }
        }

        // If we've exhausted all attempts, throw the last error
        console.error(`❌ All streaming fallback attempts failed`)
        throw lastError || new Error('All streaming fallback attempts failed')
    }

    /**
     * Get system status
     */
    getStatus(): {
        currentProvider: string
        availableProviders: string[]
        failedProviders: string[]
        hasFallback: boolean
    } {
        return {
            currentProvider: this.currentProvider,
            availableProviders: Array.from(this.providers.keys()),
            failedProviders: Array.from(this.failedProviders),
            hasFallback: this.hasFallbackAvailable()
        }
    }

    /**
     * Reset all failed providers (for manual recovery)
     */
    resetFailedProviders(): void {
        this.failedProviders.clear()
        console.log('✅ Reset all failed provider statuses')
    }

    /**
     * Force switch to specific provider
     */
    switchToProvider(providerName: string): boolean {
        if (!this.providers.has(providerName)) {
            console.error(`❌ Provider ${providerName} not available`)
            return false
        }

        this.currentProvider = providerName
        console.log(`🔄 Manually switched to ${providerName}`)
        return true
    }
}

// Export singleton instance
export const fallbackManager = new FallbackManager()
