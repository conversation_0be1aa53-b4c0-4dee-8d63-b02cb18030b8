# Project Structure

## Root Directory Organization

```
guided-tutor/
├── app/                    # Next.js App Router (pages & API routes)
├── components/             # Reusable React components
├── hooks/                  # Custom React hooks
├── lib/                    # Utility functions and configurations
├── prisma/                 # Database schema and migrations
├── public/                 # Static assets (images, icons, etc.)
├── scripts/                # Build and deployment scripts
├── styles/                 # Global CSS files
├── tests/                  # Automated tests (unit, integration, e2e)
└── .kiro/                  # Kiro AI assistant configuration
```

## App Directory (Next.js App Router)

```
app/
├── globals.css             # Global styles and CSS variables
├── layout.tsx              # Root layout component
├── page.tsx                # Landing page
├── api/                    # API routes
│   ├── chat/               # Chat-related endpoints
│   └── documents/          # Document processing endpoints
├── auth/                   # Authentication pages
│   ├── page.tsx            # Auth page (signup/login)
│   ├── callback/           # OAuth callback handling
│   └── verify-email/       # Email verification page
└── dashboard/              # Protected dashboard area
    ├── layout.tsx          # Dashboard layout with sidebar
    └── page.tsx            # Main dashboard page
```

## Components Organization

```
components/
├── ui/                     # shadcn/ui base components
│   ├── button.tsx          # Button variants and styles
│   ├── card.tsx            # Card layouts
│   ├── form.tsx            # Form components
│   ├── input.tsx           # Input fields
│   └── ...                 # Other UI primitives
├── auth/                   # Authentication components
│   ├── login-form.tsx      # Login form component
│   └── signup-form.tsx     # Signup form component
├── dashboard/              # Dashboard-specific components
│   ├── document-upload.tsx # File upload functionality
│   ├── document-list.tsx   # Document listing
│   └── dashboard-nav.tsx   # Dashboard navigation
├── chat/                   # Chat interface components
│   └── chat-interface.tsx  # Main chat component
├── app-sidebar.tsx         # Main application sidebar
├── mobile-sidebar.tsx      # Mobile-responsive sidebar
├── sidebar-context.tsx     # Sidebar state management
└── theme-provider.tsx      # Theme context provider
```

## Library Organization

```
lib/
├── utils.ts                # Utility functions (cn helper)
├── prisma.ts               # Prisma client configuration
├── db-service.ts           # Database service layer
└── supabase/               # Supabase client configurations
```

## Naming Conventions

### Files & Directories
- **kebab-case** for file names: `document-upload.tsx`, `user-profile.tsx`
- **camelCase** for utility files: `dbService.ts`, `apiHelpers.ts`
- **lowercase** for directories: `components/`, `hooks/`, `lib/`

### Components
- **kebab-case** for component file names: `document-upload.tsx`
- **PascalCase** for exported component names: `DocumentUpload`
- **camelCase** for props and variables: `userId`, `documentList`
- **SCREAMING_SNAKE_CASE** for constants: `MAX_FILE_SIZE`

### Database
- **snake_case** for table and column names: `user_id`, `created_at`
- **camelCase** in TypeScript interfaces: `userId`, `createdAt`

## Import Patterns

### Path Aliases (configured in tsconfig.json)
```typescript
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"
```

### Component Imports
```typescript
// UI components
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

// Feature components
import { DocumentUpload } from "@/components/dashboard/document-upload"
```

## Testing Organization

```
tests/
├── __mocks__/              # Mock files for testing
├── unit/                   # Unit tests for individual components/functions
│   ├── components/         # Component unit tests
│   ├── hooks/              # Custom hook tests
│   └── lib/                # Utility function tests
├── integration/            # Integration tests for API routes and features
│   ├── api/                # API endpoint tests
│   └── database/           # Database integration tests
├── e2e/                    # End-to-end tests
│   ├── auth.spec.ts        # Authentication flow tests
│   ├── dashboard.spec.ts   # Dashboard functionality tests
│   └── chat.spec.ts        # Chat interface tests
├── fixtures/               # Test data and fixtures
└── setup/                  # Test configuration and setup files
```

### Testing Conventions
- **Unit Tests**: `.test.ts` or `.test.tsx` suffix
- **Integration Tests**: `.integration.test.ts` suffix
- **E2E Tests**: `.spec.ts` suffix (Playwright/Cypress)
- **Mock Files**: Mirror the structure of actual modules
- **Test Data**: JSON fixtures in `tests/fixtures/`

### Alternative: Colocated Tests
For smaller components, tests can be colocated using `__tests__/` directories:
```
components/
├── ui/
│   ├── button.tsx
│   └── __tests__/
│       └── button.test.tsx
```

## Architecture Patterns

### Component Structure
- **UI Components**: Pure, reusable components in `components/ui/`
- **Feature Components**: Business logic components organized by feature
- **Layout Components**: Page layouts and navigation components
- **Provider Components**: Context providers for global state

### Data Flow
- **Server Components**: Default for data fetching and static content
- **Client Components**: For interactivity and browser APIs
- **API Routes**: RESTful endpoints in `app/api/`
- **Database Layer**: Prisma ORM with Supabase PostgreSQL

### State Management
- **React Context**: For global UI state (theme, sidebar)
- **Server State**: Handled by Next.js and Supabase
- **Form State**: React Hook Form for complex forms
- **Local State**: useState for component-specific state