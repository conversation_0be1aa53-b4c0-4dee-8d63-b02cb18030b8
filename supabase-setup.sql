-- This script sets up proper relationship between auth.users and custom tables
-- Run this in your Supabase SQL Editor

-- First, drop existing problematic constraints
ALTER TABLE public.profiles DROP CONSTRAINT IF EXISTS profiles_userId_fkey;
ALTER TABLE public.user DROP CONSTRAINT IF EXISTS user_id_fkey;

-- Add proper foreign key constraints with CASCADE DELETE
-- This ensures when auth.users is deleted, everything cascades down

-- Step 1: user table references auth.users with CASCADE
ALTER TABLE public.user 
ADD CONSTRAINT user_id_fkey 
FOREIGN KEY (id) REFERENCES auth.users(id) ON DELETE CASCADE;

-- Step 2: profiles table references user table with CASCADE  
ALTER TABLE public.profiles 
ADD CONSTRAINT profiles_userId_fkey 
FOREIGN KEY ("userId") REFERENCES public.user(id) ON DELETE CASCADE;

-- Create a function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Insert into your custom user table using the same ID from auth.users
  INSERT INTO public.user (id, email, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    NOW(),
    NOW()
  );
  
  -- Insert into your profile table
  INSERT INTO public.profiles (id, "userId", "fullName", school, created_at, updated_at)
  VALUES (
    gen_random_uuid(),
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    COALESCE(NEW.raw_user_meta_data->>'school', ''),
    NOW(),
    NOW()
  );
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a trigger that fires when a new user is created in auth.users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL TABLES IN SCHEMA public TO postgres, anon, authenticated, service_role;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO postgres, anon, authenticated, service_role;

-- Enable Row Level Security (RLS) for better security
ALTER TABLE public.user ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist, then recreate them
DROP POLICY IF EXISTS "Users can view own user record" ON public.user;
DROP POLICY IF EXISTS "Users can update own user record" ON public.user;
DROP POLICY IF EXISTS "Users can view own profile" ON public.profiles;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;

-- Create RLS policies so users can only access their own data
CREATE POLICY "Users can view own user record" ON public.user
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own user record" ON public.user
  FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own profile" ON public.profiles
  FOR SELECT USING (auth.uid() = "userId");

CREATE POLICY "Users can update own profile" ON public.profiles
  FOR UPDATE USING (auth.uid() = "userId");