"use client"

import type React from "react"

import { useState, useRef } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Upload, Loader2 } from "lucide-react"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface CompactUploadProps {
  onUploadComplete: (documentId: string, filename: string) => void
}

export function CompactUpload({ onUploadComplete }: CompactUploadProps) {
  const [uploading, setUploading] = useState(false)
  const [error, setError] = useState("")
  const [uploadStage, setUploadStage] = useState("")
  const [progress, setProgress] = useState(0)
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    const supportedExtensions = ['.pdf', '.docx', '.doc', '.pptx', '.ppt', '.txt', '.md', '.rtf', '.html', '.htm']
    const fileExtension = file.name.toLowerCase().substring(file.name.lastIndexOf('.'))
    
    if (!supportedExtensions.includes(fileExtension)) {
      setError(`Unsupported file type. Please upload: PDF, Word, PowerPoint, Text, Markdown, RTF, or HTML files.`)
      return
    }

    // Validate file size (50MB limit)
    const maxSize = 50 * 1024 * 1024 // 50MB in bytes
    if (file.size > maxSize) {
      setError(`File too large. Maximum size is 50MB.`)
      return
    }

    setUploading(true)
    setError("")
    setProgress(0)

    try {
      // Stage 1: Uploading file
      setUploadStage("Uploading file...")
      setProgress(20)
      
      const formData = new FormData()
      formData.append("file", file)

      const response = await fetch("/api/documents/upload", {
        method: "POST",
        body: formData,
      })

      const data = await response.json()

      if (!response.ok) {
        throw new Error(data.error || "Upload failed")
      }

      // Stage 2: Processing with LlamaParse
      setUploadStage("Processing document with AI...")
      setProgress(40)
      
      // Poll for processing completion
      await pollProcessingStatus(data.documentId)
      
      setUploadStage("Complete!")
      setProgress(100)
      
      setTimeout(() => {
        onUploadComplete(data.documentId, file.name)
        
        // Trigger a manual refresh of the document list
        window.dispatchEvent(new CustomEvent("documentsUpdated"))
      }, 500)
      
    } catch (error: any) {
      console.error("Upload error:", error)
      setError(error.message || "Failed to upload file. Please try again.")
    } finally {
      setTimeout(() => {
        setUploading(false)
        setUploadStage("")
        setProgress(0)
        // Reset the input
        if (fileInputRef.current) {
          fileInputRef.current.value = ""
        }
      }, 1000)
    }
  }

  const pollProcessingStatus = async (documentId: string) => {
    const maxAttempts = 30 // 30 seconds max
    let attempts = 0
    
    while (attempts < maxAttempts) {
      try {
        const response = await fetch(`/api/documents/${documentId}/status`)
        const data = await response.json()
        
        if (data.status === 'READY') {
          setUploadStage("Document ready!")
          setProgress(90)
          return
        } else if (data.status === 'ERROR') {
          throw new Error(data.errorMessage || 'Processing failed')
        }
        
        // Update progress based on processing stage
        const progressValue = 40 + (attempts * 2) // Gradually increase from 40% to 80%
        setProgress(Math.min(progressValue, 80))
        
        if (attempts < 5) {
          setUploadStage("Parsing document structure...")
        } else if (attempts < 15) {
          setUploadStage("Extracting content chunks...")
        } else {
          setUploadStage("Finalizing processing...")
        }
        
        await new Promise(resolve => setTimeout(resolve, 1000)) // Wait 1 second
        attempts++
      } catch (error) {
        throw error
      }
    }
    
    throw new Error('Processing timeout - please try again')
  }

  const handleClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <div className="flex flex-col items-center gap-6">
      <input
        ref={fileInputRef}
        type="file"
        accept=".pdf,.docx,.doc,.pptx,.ppt,.txt,.md,.rtf,.html,.htm"
        onChange={handleFileSelect}
        className="hidden"
      />

      <Button
        onClick={handleClick}
        disabled={uploading}
        variant="outline"
        size="lg"
        className={`${uploading ? 'h-20 w-80' : 'h-14 w-64'} rounded-lg border-2 border-solid border-muted-foreground/40 hover:border-primary hover:bg-primary/5 transition-all duration-200 bg-white font-body`}
      >
        {uploading ? (
          <div className="flex flex-col items-center gap-2 w-full">
            <div className="flex items-center gap-3">
              <Loader2 className="h-5 w-5 animate-spin text-primary" />
              <span className="text-primary font-medium">{uploadStage}</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-primary h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
            <span className="text-sm text-gray-500">{progress}%</span>
          </div>
        ) : (
          <div className="flex items-center gap-3">
            <Upload className="h-5 w-5 text-muted-foreground" />
            <span className="text-muted-foreground font-medium">Upload Document</span>
          </div>
        )}
      </Button>

      <p className="text-sm text-muted-foreground text-center font-body">
        PDF, Word, PowerPoint, Text, Markdown, RTF, HTML (max 50MB)
      </p>

      {error && (
        <Alert variant="destructive" className="max-w-md">
          <AlertDescription className="font-body">{error}</AlertDescription>
        </Alert>
      )}
    </div>
  )
}
