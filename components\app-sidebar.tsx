"use client"
import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus, Settings, LogOut, GraduationCap } from "lucide-react"
import { DocumentList } from "@/components/dashboard/document-list"

import { createClient } from "@/lib/supabase/client"
import { useRouter } from "next/navigation"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import type { User as SupabaseUser } from "@supabase/supabase-js"

interface AppSidebarProps {
  user: SupabaseUser
}

export function AppSidebar({ user }: AppSidebarProps) {
  const [refreshDocuments, setRefreshDocuments] = useState(0)
  const [loadingSignOut, setLoadingSignOut] = useState(false)
  const supabase = createClient()
  const router = useRouter()
  // Removed useSidebar state since we're not using collapsible functionality

  const handleUploadComplete = () => {
    setRefreshDocuments((prev) => prev + 1)
  }

  const handleSignOut = async () => {
    setLoadingSignOut(true)
    await supabase.auth.signOut()
    router.push("/auth")
    router.refresh()
  }

  const getInitials = (email: string) => {
    return email.split("@")[0].slice(0, 2).toUpperCase()
  }

  return (
    <>
      <div className="bg-[#F9FAFB] text-sidebar-foreground border-r border-sidebar-border shadow-sm w-64 h-full flex flex-col relative overflow-hidden">
        {/* Header - Better spacing */}
        <div className="border-b border-sidebar-border">
          <div className="flex items-center gap-3 px-4 py-3">
            <div className="p-2 bg-primary rounded-lg border border-white">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <span className="text-xl font-semibold text-foreground font-heading">Guided Tutor</span>
          </div>
          <div className="px-4 pb-6 pt-2">
            <Button
              onClick={() => window.dispatchEvent(new CustomEvent("navigateToUpload"))}
              className="bg-primary hover:bg-primary/90 text-white rounded-lg font-body w-full px-4 py-3 text-sm font-medium"
            >
              <Plus className="h-4 w-4 mr-2" />
              <span>New Document</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-hidden border-r-0">
          <div className="p-4">
            <DocumentList refreshTrigger={refreshDocuments} />
          </div>
        </div>

        {/* Footer with fixed alignment */}
        <div className="p-4 mt-auto border-t-0">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="w-full justify-start p-3 h-auto rounded-lg hover:bg-gray-100 border-0">
                <Avatar className="h-8 w-8 mr-3">
                  {/* Profile icon color consistent with primary theme */}
                  <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
                    {getInitials(user.email || "")}
                  </AvatarFallback>
                </Avatar>
                <span className="truncate font-body text-sm">{user.email}</span>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              side="top"
              className="w-[--radix-popper-anchor-width] bg-white text-card-foreground border border-border shadow-sm"
            >
              <DropdownMenuItem className="hover:bg-accent hover:text-accent-foreground font-body">
                <Settings className="mr-2 h-4 w-4" />
                <span>Settings</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border" />
              <DropdownMenuItem
                onClick={handleSignOut}
                disabled={loadingSignOut}
                className="text-destructive hover:bg-destructive/10 hover:text-destructive font-body"
              >
                <LogOut className="mr-2 h-4 w-4" />
                <span>Sign out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

    </>
  )
}
