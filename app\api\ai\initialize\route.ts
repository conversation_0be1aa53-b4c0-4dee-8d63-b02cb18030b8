import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { documentAIInitializer } from '@/lib/ai/document-ai-initializer'
import { dbService } from '@/lib/db-service'

/**
 * AI Initialization API Endpoint
 * Handles manual AI initialization for documents
 */

/**
 * POST /api/ai/initialize
 * Initialize AI tutoring for a specific document
 */
export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Parse request body
    const { documentId, forceReinitialize = false } = await request.json()

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Check if document is ready for AI initialization
    const isReady = await documentAIInitializer.isDocumentReadyForAI(documentId)
    if (!isReady) {
      return NextResponse.json(
        {
          error: 'Document is not ready for AI initialization. Please wait for processing to complete.',
          status: document.status
        },
        { status: 400 }
      )
    }

    // Check if AI is already initialized (unless force reinitialize)
    if (!forceReinitialize) {
      const isInitialized = await documentAIInitializer.isAIInitialized(
        documentId,
        session.user.id
      )

      if (isInitialized) {
        return NextResponse.json(
          {
            message: 'AI is already initialized for this document',
            alreadyInitialized: true
          }
        )
      }
    }

    // Initialize or reinitialize AI
    const result = forceReinitialize
      ? await documentAIInitializer.reinitializeAI(documentId, session.user.id, document.fileName)
      : await documentAIInitializer.initializeAIForDocument(documentId, session.user.id, document.fileName)

    if (result.success) {
      return NextResponse.json({
        success: true,
        message: 'AI initialization completed successfully',
        sessionId: result.sessionId,
        initialResponse: result.initialResponse,
        chunkContent: result.chunkContent
      })
    } else {
      return NextResponse.json(
        {
          error: 'AI initialization failed',
          details: result.error
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error('AI initialization API error:', error)

    return NextResponse.json(
      {
        error: 'Internal server error during AI initialization',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

/**
 * GET /api/ai/initialize?documentId=xxx
 * Check AI initialization status for a document
 */
export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get document ID from query parameters
    const { searchParams } = new URL(request.url)
    const documentId = searchParams.get('documentId')

    if (!documentId) {
      return NextResponse.json(
        { error: 'Document ID is required' },
        { status: 400 }
      )
    }

    // Verify document exists and belongs to user
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: session.user.id
      }
    })

    if (!document) {
      return NextResponse.json(
        { error: 'Document not found or access denied' },
        { status: 404 }
      )
    }

    // Check document readiness and AI initialization status
    const [isReady, isInitialized] = await Promise.all([
      documentAIInitializer.isDocumentReadyForAI(documentId),
      documentAIInitializer.isAIInitialized(documentId, session.user.id)
    ])

    return NextResponse.json({
      documentId,
      documentName: document.fileName,
      documentStatus: document.status,
      isReady,
      isInitialized,
      totalChunks: document.totalChunks,
      canInitialize: isReady && !isInitialized
    })

  } catch (error) {
    console.error('AI initialization status check error:', error)

    return NextResponse.json(
      {
        error: 'Failed to check AI initialization status',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
