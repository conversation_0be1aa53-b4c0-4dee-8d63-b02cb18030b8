"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Send, ArrowRight, Loader2, ArrowLeft, RotateCcw, Wifi, WifiOff, ArrowDown, Download, FileText } from "lucide-react"

interface Message {
  id: string
  role: "user" | "assistant" | "system"
  content: string
  timestamp: string
  isStreaming?: boolean
}

interface ChatInterfaceProps {
  documentId: string
  documentName: string
  onBack: () => void
}

export function ChatInterface({ documentId, documentName, onBack }: ChatInterfaceProps) {
  const [messages, setMessages] = useState<Message[]>([])
  const [input, setInput] = useState("")
  const [loading, setLoading] = useState(false)
  const [currentChunk, setCurrentChunk] = useState(0)
  const [totalChunks, setTotalChunks] = useState(0)
  const [canProceed, setCanProceed] = useState(false)
  const [sessionLoading, setSessionLoading] = useState(true)
  const [retryingMessage, setRetryingMessage] = useState<string | null>(null)
  const [connectionError, setConnectionError] = useState(false)
  const [showScrollToBottom, setShowScrollToBottom] = useState(false)
  const [showExportMenu, setShowExportMenu] = useState(false)
  const [exporting, setExporting] = useState(false)
  const scrollAreaRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    initializeSession()
  }, [documentId])

  useEffect(() => {
    if (scrollAreaRef.current) {
      const scrollArea = scrollAreaRef.current
      const isNearBottom = scrollArea.scrollTop + scrollArea.clientHeight >= scrollArea.scrollHeight - 100

      if (isNearBottom) {
        scrollArea.scrollTo({
          top: scrollArea.scrollHeight,
          behavior: 'smooth'
        })
        setShowScrollToBottom(false)
      } else {
        setShowScrollToBottom(true)
      }
    }
  }, [messages])

  const scrollToBottom = () => {
    if (scrollAreaRef.current) {
      scrollAreaRef.current.scrollTo({
        top: scrollAreaRef.current.scrollHeight,
        behavior: 'smooth'
      })
      setShowScrollToBottom(false)
    }
  }

  const handleScroll = () => {
    if (scrollAreaRef.current) {
      const scrollArea = scrollAreaRef.current
      const isNearBottom = scrollArea.scrollTop + scrollArea.clientHeight >= scrollArea.scrollHeight - 100
      setShowScrollToBottom(!isNearBottom)
    }
  }

  const exportConversation = async (format: 'json' | 'text') => {
    try {
      setExporting(true)
      const response = await fetch('/api/chat/export', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          documentId,
          format,
          includeMetadata: true
        })
      })

      if (!response.ok) throw new Error('Export failed')

      const data = await response.json()
      
      // Download the exported data
      const content = format === 'json' 
        ? JSON.stringify(data.data, null, 2)
        : data.data.content

      const blob = new Blob([content], { 
        type: format === 'json' ? 'application/json' : 'text/plain' 
      })
      
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `chat-history-${documentName.replace(/[^a-z0-9]/gi, '_').toLowerCase()}-${new Date().toISOString().split('T')[0]}.${format}`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)

    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setExporting(false)
      setShowExportMenu(false)
    }
  }

  const initializeSession = async () => {
    try {
      const response = await fetch(`/api/chat/initialize`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ documentId }),
      })

      if (!response.ok) throw new Error("Failed to initialize session")

      const data = await response.json()
      setMessages(data.messages || [])
      setCurrentChunk(data.currentChunk || 0)
      setTotalChunks(data.totalChunks || 0)
      setCanProceed(data.canProceed || false)
    } catch (error) {
      console.error("Failed to initialize session:", error)
    } finally {
      setSessionLoading(false)
    }
  }

  const sendMessage = async () => {
    if (!input.trim() || loading) return

    const userMessage = input.trim()
    setInput("")
    setLoading(true)

    // Add user message immediately
    const newUserMessage: Message = {
      id: Date.now().toString(),
      role: "user",
      content: userMessage,
      timestamp: new Date().toISOString(),
    }
    setMessages((prev) => [...prev, newUserMessage])

    // Create placeholder for AI response that will be streamed
    const aiMessageId = (Date.now() + 1).toString()
    const aiMessage: Message = {
      id: aiMessageId,
      role: "assistant",
      content: "",
      timestamp: new Date().toISOString(),
      isStreaming: true,
    }
    setMessages((prev) => [...prev, aiMessage])

    try {
      const response = await fetch("/api/chat/stream", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId,
          chunkIndex: currentChunk,
          message: userMessage,
        }),
      })

      if (!response.ok) throw new Error("Failed to send message")

      const reader = response.body?.getReader()
      const decoder = new TextDecoder()

      if (!reader) throw new Error("No response body")

      let accumulatedContent = ""
      let canProceedValue = false

      while (true) {
        const { done, value } = await reader.read()
        if (done) break

        const chunk = decoder.decode(value)
        const lines = chunk.split('\n')

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            try {
              const data = JSON.parse(line.slice(6))

              if (data.type === 'token') {
                accumulatedContent += data.content
                // Update the AI message with accumulated content
                setMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === aiMessageId
                      ? { ...msg, content: accumulatedContent }
                      : msg
                  )
                )
              } else if (data.type === 'complete') {
                // Mark streaming as complete and update proceed status
                setMessages((prev) =>
                  prev.map((msg) =>
                    msg.id === aiMessageId
                      ? { ...msg, isStreaming: false }
                      : msg
                  )
                )
                // Use the canProceed status from AI analysis
                canProceedValue = data.canProceed || false
                setCanProceed(canProceedValue)
              } else if (data.type === 'error') {
                throw new Error(data.message || 'Streaming error')
              }
            } catch (parseError) {
              // Skip malformed JSON lines
              continue
            }
          }
        }
      }
    } catch (error) {
      console.error("Failed to send message:", error)
      setConnectionError(true)

      // Determine error type for better user feedback
      const isNetworkError = error instanceof TypeError && error.message.includes('fetch')
      const errorMessage = isNetworkError
        ? "Connection lost. Please check your internet connection and try again."
        : "I'm sorry, I encountered an error. Please try again."

      // Update the AI message with error content
      setMessages((prev) =>
        prev.map((msg) =>
          msg.id === aiMessageId
            ? { ...msg, content: errorMessage, isStreaming: false }
            : msg
        )
      )

      // Clear connection error after a delay
      setTimeout(() => setConnectionError(false), 5000)
    } finally {
      setLoading(false)
    }
  }

  const proceedToNextChunk = async () => {
    if (!canProceed || loading) return

    setLoading(true)
    setCanProceed(false)

    try {
      const response = await fetch("/api/chat/next-chunk", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          documentId,
          currentChunk,
          direction: "next"
        }),
      })

      if (!response.ok) throw new Error("Failed to proceed to next chunk")

      const data = await response.json()

      if (data.completed) {
        // Session completed
        const completionMessage: Message = {
          id: Date.now().toString(),
          role: "system",
          content: "🎉 Congratulations! You've completed studying this document. Great job!",
          timestamp: new Date().toISOString(),
        }
        setMessages((prev) => [...prev, completionMessage])
      } else {
        // Move to next chunk and add AI transition response
        setCurrentChunk(data.currentChunk)

        // Add a system message indicating chunk transition
        const transitionMessage: Message = {
          id: Date.now().toString(),
          role: "system",
          content: `📖 Moving to Section ${data.currentChunk + 1} of ${data.totalChunks}`,
          timestamp: new Date().toISOString(),
        }
        setMessages((prev) => [...prev, transitionMessage])

        // Add AI introduction for new chunk with memory context
        const aiMessageId = (Date.now() + 1).toString()
        const aiMessage: Message = {
          id: aiMessageId,
          role: "assistant",
          content: data.response || "",
          timestamp: new Date().toISOString(),
          isStreaming: false,
        }
        setMessages((prev) => [...prev, aiMessage])

        // Update progress information
        if (data.progress) {
          console.log(`Progress: ${data.progress.current}/${data.progress.total} (${data.progress.percentage}%)`)
        }

        // Log memory context information for debugging
        if (data.memoryContext) {
          console.log('Memory context available:', data.memoryContext.hasMemoryContext)
          console.log('Context summary:', data.memoryContext.contextSummary)
        }
      }
    } catch (error) {
      console.error("Failed to proceed:", error)
      const errorMessage: Message = {
        id: Date.now().toString(),
        role: "assistant",
        content: "I'm sorry, I encountered an error while moving to the next section. Please try again.",
        timestamp: new Date().toISOString(),
      }
      setMessages((prev) => [...prev, errorMessage])
    } finally {
      setLoading(false)
    }
  }

  const retryMessage = async (messageId: string) => {
    const messageToRetry = messages.find(msg => msg.id === messageId && msg.role === "user")
    if (!messageToRetry || retryingMessage) return

    setRetryingMessage(messageId)

    // Find the AI response that follows this user message
    const messageIndex = messages.findIndex(msg => msg.id === messageId)
    const aiResponseIndex = messageIndex + 1

    if (aiResponseIndex < messages.length && messages[aiResponseIndex].role === "assistant") {
      // Remove the failed AI response
      setMessages(prev => prev.filter((_, index) => index !== aiResponseIndex))
    }

    // Simulate sending the message again
    setInput(messageToRetry.content)
    setTimeout(() => {
      sendMessage()
      setRetryingMessage(null)
    }, 100)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault()
      sendMessage()
    }
  }

  const progressValue = totalChunks > 0 ? ((currentChunk + 1) / totalChunks) * 100 : 0

  if (sessionLoading) {
    return (
      <Card className="h-[600px] flex items-center justify-center bg-white text-card-foreground border border-border shadow-sm">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <p className="text-muted-foreground font-body">Initializing your study session...</p>
        </div>
      </Card>
    )
  }

  return (
    <div className="h-full flex flex-col p-4">
      {/* Top Navbar for Chat */}
      <div className="flex items-center justify-between mb-4 p-4 bg-white border border-border rounded-lg shadow-sm">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={onBack}
            className="text-muted-foreground hover:bg-accent hover:text-accent-foreground font-body"
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Back
          </Button>
          <div>
            <h2 className="text-lg font-heading font-semibold">{documentName}</h2>
            <div className="flex items-center gap-2 mt-1">
              <Badge variant="secondary" className="bg-muted text-muted-foreground font-body">
                Chunk {currentChunk + 1} of {totalChunks}
              </Badge>
              <Badge variant="outline" className="text-xs font-body">
                {messages.filter(m => m.role === 'user').length} messages
              </Badge>
              <span className="text-sm text-muted-foreground font-body">{Math.round(progressValue)}% complete</span>
              {canProceed && (
                <Badge variant="default" className="bg-green-100 text-green-800 text-xs font-body">
                  Ready to proceed
                </Badge>
              )}
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2">
          {/* Export Menu */}
          <div className="relative">
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowExportMenu(!showExportMenu)}
              disabled={exporting || messages.length === 0}
              className="text-muted-foreground hover:bg-accent hover:text-accent-foreground font-body"
            >
              {exporting ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Download className="w-4 h-4" />
              )}
            </Button>
            
            {showExportMenu && (
              <div className="absolute top-full right-0 mt-1 bg-white border border-border rounded-md shadow-lg z-10">
                <div className="p-2 space-y-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => exportConversation('json')}
                    disabled={exporting}
                    className="w-full justify-start text-sm font-body"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Export as JSON
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => exportConversation('text')}
                    disabled={exporting}
                    className="w-full justify-start text-sm font-body"
                  >
                    <FileText className="w-4 h-4 mr-2" />
                    Export as Text
                  </Button>
                </div>
              </div>
            )}
          </div>

          {canProceed && (
            <Button
              onClick={proceedToNextChunk}
              disabled={loading}
              className="bg-primary hover:bg-primary/90 text-white px-4 py-2 text-sm font-body animate-pulse"
            >
              Next Section <ArrowRight className="w-4 h-4 ml-2" />
            </Button>
          )}
        </div>
      </div>

      <div className="mb-4">
        <Progress value={progressValue} className="w-full h-2" />
        {loading && (
          <div className="flex items-center justify-center mt-2 text-xs text-muted-foreground">
            <Loader2 className="w-3 h-3 animate-spin mr-1" />
            AI is thinking...
          </div>
        )}
      </div>

      {/* Chat Area - Takes remaining space */}
      <div className="flex-1 flex flex-col bg-white border border-border rounded-lg shadow-sm min-h-0">
        <div className="flex-1 relative">
          <ScrollArea className="flex-1 p-4" ref={scrollAreaRef} onScroll={handleScroll}>
            <div className="space-y-4">
              {messages.length === 0 && (
                <div className="text-center text-muted-foreground py-8">
                  <p className="font-body">Start a conversation about this document...</p>
                </div>
              )}
              {messages.map((message) => (
                <div key={message.id} className={`flex ${message.role === "user" ? "justify-start" : "justify-end"}`}>
                  <div
                    className={`max-w-[80%] rounded-lg px-4 py-2 ${message.role === "user"
                      ? "bg-primary text-primary-foreground" // User messages on left
                      : message.role === "system"
                        ? "bg-secondary text-secondary-foreground border border-border"
                        : "bg-muted text-muted-foreground" // AI messages on right
                      } font-body`}
                  >
                    <div className="flex items-start gap-2">
                      <p className="whitespace-pre-wrap flex-1">
                        {message.content}
                        {message.isStreaming && message.content === "" && (
                          <span className="flex items-center gap-1">
                            <span className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                            <span className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                            <span className="w-2 h-2 bg-current rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                          </span>
                        )}
                        {message.isStreaming && message.content !== "" && (
                          <span className="inline-block w-2 h-4 bg-current opacity-75 animate-pulse ml-1" />
                        )}
                      </p>
                    </div>
                    <div className="flex items-center justify-between mt-1">
                      <p
                        className={`text-xs ${message.role === "user" ? "text-primary-foreground/80" : "text-muted-foreground/80"}`}
                      >
                        {new Date(message.timestamp).toLocaleTimeString()}
                      </p>
                      <div className="flex items-center gap-2">
                        {message.isStreaming && (
                          <div className="flex items-center gap-1 text-xs opacity-60">
                            <Loader2 className="w-3 h-3 animate-spin" />
                            <span>Typing...</span>
                          </div>
                        )}
                        {message.role === "assistant" &&
                          message.content.includes("I'm sorry, I encountered an error") &&
                          !message.isStreaming && (
                            <Button
                              size="sm"
                              variant="ghost"
                              onClick={() => {
                                // Find the previous user message to retry
                                const messageIndex = messages.findIndex(msg => msg.id === message.id)
                                const userMessageIndex = messageIndex - 1
                                if (userMessageIndex >= 0 && messages[userMessageIndex].role === "user") {
                                  retryMessage(messages[userMessageIndex].id)
                                }
                              }}
                              disabled={retryingMessage !== null}
                              className="h-6 px-2 text-xs opacity-60 hover:opacity-100"
                            >
                              <RotateCcw className="w-3 h-3 mr-1" />
                              Retry
                            </Button>
                          )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}

            </div>
          </ScrollArea>

          {showScrollToBottom && (
            <Button
              onClick={scrollToBottom}
              size="sm"
              className="absolute bottom-4 right-4 rounded-full bg-primary hover:bg-primary/90 text-white shadow-lg"
            >
              <ArrowDown className="w-4 h-4" />
            </Button>
          )}
        </div>

        {/* Input Area - Fixed at bottom */}
        <div className="border-t border-border p-4 bg-white rounded-b-lg">
          {connectionError && (
            <div className="mb-3 p-2 bg-destructive/10 border border-destructive/20 rounded-md flex items-center gap-2 text-sm text-destructive">
              <WifiOff className="w-4 h-4" />
              Connection error. Please check your internet connection.
            </div>
          )}
          <div className="flex items-center relative">
            <Input
              value={input}
              onChange={(e) => setInput(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Type your response or ask a question..."
              disabled={loading}
              className="flex-1 bg-white text-foreground border-border focus:ring-primary pr-10 font-body"
            />
            <Button
              onClick={sendMessage}
              disabled={loading || !input.trim()}
              size="icon"
              className="absolute right-2 bg-primary hover:bg-primary/90 text-white h-8 w-8"
            >
              {loading ? (
                <Loader2 className="w-4 h-4 animate-spin" />
              ) : (
                <Send className="w-4 h-4" />
              )}
            </Button>
          </div>
          <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
            <div className="flex items-center gap-1">
              {connectionError ? (
                <WifiOff className="w-3 h-3 text-destructive" />
              ) : (
                <Wifi className="w-3 h-3 text-green-500" />
              )}
              <span>{connectionError ? "Disconnected" : "Connected"}</span>
            </div>
            <span>Press Enter to send</span>
          </div>
        </div>
      </div>
    </div>
  )
}
