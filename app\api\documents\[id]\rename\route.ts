import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const supabase = await createClient()
    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: documentId } = await params
    const { newFilename } = await request.json()

    if (!newFilename || typeof newFilename !== "string" || newFilename.trim() === "") {
      return NextResponse.json({ error: "Invalid filename provided" }, { status: 400 })
    }

    // Verify ownership and update filename
    const updatedDocument = await dbService.document.update({
      where: {
        id: documentId,
        userId: session.user.id,
      },
      data: {
        fileName: newFilename.trim(),
      },
    })

    if (!updatedDocument) {
      return NextResponse.json({ error: "Document not found or unauthorized" }, { status: 404 })
    }

    return NextResponse.json({
      id: updatedDocument.id,
      fileName: updatedDocument.fileName,
    })
  } catch (error) {
    console.error("Rename document error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
