"use client" // Add this line to make the component a Client Component

import type React from "react"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Label } from "@/components/ui/label"
import {
  GraduationCap,
  Brain,
  Upload,
  MessageCircle,
  ArrowRight,
  Check,
  Mail,
  Phone,
  MapPin,
  BookOpen,
  Lightbulb,
  Target,
  Menu,
} from "lucide-react"
// Remove createServerClient and redirect as this will now be a client component
// import { createServerClient } from "@/lib/supabase"
// import { redirect } from "next/navigation"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { useRef } from "react" // Import useRef

export default function HomePage() {
  // Change to a client component function
  // Remove supabase and session logic as it's no longer a Server Component
  // const supabase = createServerClient()
  // const {
  //   data: { session },
  // } = await supabase.auth.getSession()

  // If a session exists, redirect to the dashboard
  // if (session) {
  //   redirect("/dashboard")
  // }

  const pricingRef = useRef<HTMLElement>(null) // Create a ref for the pricing section

  const scrollToPricing = (e: React.MouseEvent<HTMLAnchorElement>) => {
    e.preventDefault() // Prevent default link behavior
    if (pricingRef.current) {
      pricingRef.current.scrollIntoView({ behavior: "smooth" }) // Smooth scroll to the section
    }
  }

  return (
    <div className="min-h-screen bg-white text-foreground font-body">
      {/* Header/Navbar */}
      <header className="w-full py-6 px-6 border-b border-border bg-white">
        <div className="container mx-auto flex items-center justify-between max-w-6xl">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-primary rounded-lg">
              <GraduationCap className="h-6 w-6 text-white" />
            </div>
            <h1 className="text-xl font-semibold text-foreground font-heading">Guided Tutor</h1>
          </div>
          <nav className="hidden md:flex items-center gap-6">
            <Link
              href="#pricing"
              onClick={scrollToPricing} // Add onClick handler
              className="text-muted-foreground hover:text-foreground transition-colors font-body"
            >
              Pricing
            </Link>
            <Button asChild variant="ghost" className="text-foreground hover:bg-accent font-body">
              <Link href="/auth">Sign In</Link>
            </Button>
            <Button asChild className="bg-primary hover:bg-primary/90 text-white rounded-lg px-6 font-body">
              <Link href="/auth?mode=signup">Start Learning</Link>
            </Button>
          </nav>
          <div className="md:hidden">
            <Sheet>
              <SheetTrigger asChild>
                <Button variant="ghost" size="icon">
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Toggle navigation menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="right" className="w-[250px] sm:w-[300px] bg-white text-foreground">
                <div className="flex flex-col gap-4 py-6">
                  <Link
                    href="#pricing"
                    onClick={scrollToPricing} // Add onClick handler for mobile menu
                    className="text-lg font-medium hover:text-primary transition-colors font-body"
                  >
                    Pricing
                  </Link>
                  <Link href="/auth" className="text-lg font-medium hover:text-primary transition-colors font-body">
                    Sign In
                  </Link>
                  <Button asChild className="bg-primary hover:bg-primary/90 text-white rounded-lg px-6 font-body">
                    <Link href="/auth?mode=signup">Start Learning</Link>
                  </Button>
                </div>
              </SheetContent>
            </Sheet>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section with Grid Background */}
        <section className="py-20 px-6 grid-background relative overflow-hidden">
          {/* Floating Elements */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="floating-element absolute top-20 left-20 text-primary/20">
              <BookOpen className="h-12 w-12" />
            </div>
            <div className="floating-element absolute top-32 right-32 text-primary/20">
              <Lightbulb className="h-10 w-10" />
            </div>
            <div className="floating-element absolute bottom-40 left-40 text-primary/20">
              <Target className="h-8 w-8" />
            </div>
            <div className="floating-element absolute top-40 right-20 text-primary/20">
              <Brain className="h-14 w-14" />
            </div>
            <div className="floating-element absolute bottom-32 right-40 text-primary/20">
              <GraduationCap className="h-10 w-10" />
            </div>
          </div>

          <div className="container mx-auto text-center max-w-4xl relative z-10">
            <h1 className="text-5xl lg:text-6xl font-bold text-foreground mb-6 leading-tight font-heading">
              Turn your lecture notes into a <span className="text-primary">personalized tutor</span>
            </h1>
            <p className="text-xl text-muted-foreground mb-10 leading-relaxed max-w-2xl mx-auto font-body">
              Upload your course materials and engage with an AI tutor that uses the Socratic method to ensure deep
              understanding and mastery.
            </p>
            <Button
              asChild
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white text-lg px-8 py-4 rounded-lg font-body"
            >
              <Link href="/auth?mode=signup">Start Learning Today</Link>
            </Button>
          </div>
        </section>

        {/* Key Features Section */}
        <section className="py-20 px-6 bg-secondary/30">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-4 font-heading">Learning that works for you.</h2>
              <h2 className="text-4xl font-bold text-foreground font-heading">Not the other way around.</h2>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-12">
              <div className="text-center">
                <div className="p-4 bg-white rounded-xl w-fit mx-auto mb-6 shadow-sm">
                  <Upload className="h-8 w-8 text-primary" />
                </div>
                <h3 className="font-semibold text-xl text-foreground mb-3 font-heading">Smart Document Processing</h3>
                <p className="text-muted-foreground leading-relaxed font-body">
                  Upload any course material - PDFs, images, or notes. Our AI intelligently breaks down complex content
                  into digestible learning segments.
                </p>
              </div>

              <div className="text-center">
                <div className="p-4 bg-white rounded-xl w-fit mx-auto mb-6 shadow-sm">
                  <Brain className="h-8 w-8 text-primary" />
                </div>
                <h3 className="font-semibold text-xl text-foreground mb-3 font-heading">Socratic Method Learning</h3>
                <p className="text-muted-foreground leading-relaxed font-body">
                  Engage in meaningful dialogue with your AI tutor. Through thoughtful questioning, achieve deeper
                  understanding of complex concepts.
                </p>
              </div>

              <div className="text-center">
                <div className="p-4 bg-white rounded-xl w-fit mx-auto mb-6 shadow-sm">
                  <MessageCircle className="h-8 w-8 text-primary" />
                </div>
                <h3 className="font-semibold text-xl text-foreground mb-3 font-heading">Adaptive Progress Tracking</h3>
                <p className="text-muted-foreground leading-relaxed font-body">
                  Never lose your place. Our system ensures mastery of each concept before progressing to the next
                  topic.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Who This Is For Section */}
        <section className="py-20 px-6">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-4 font-heading">Perfect for every learner</h2>
              <p className="text-xl text-muted-foreground font-body">
                Whether you&apos;re a student, professional, or lifelong learner
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-12">
              <div className="bg-white p-8 rounded-xl border border-border">
                <h3 className="font-semibold text-2xl text-foreground mb-4 font-heading">University Students</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed font-body">
                  Transform dense textbooks and lecture notes into interactive learning sessions. Perfect for exam
                  preparation and concept mastery.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Break down complex theories</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Prepare for exams effectively</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Master difficult subjects</span>
                  </li>
                </ul>
              </div>

              <div className="bg-white p-8 rounded-xl border border-border">
                <h3 className="font-semibold text-2xl text-foreground mb-4 font-heading">Professionals</h3>
                <p className="text-muted-foreground mb-6 leading-relaxed font-body">
                  Stay ahead in your field by turning industry reports, research papers, and training materials into
                  personalized learning experiences.
                </p>
                <ul className="space-y-3">
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Continuous skill development</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Industry knowledge updates</span>
                  </li>
                  <li className="flex items-center gap-3">
                    <Check className="h-5 w-5 text-primary" />
                    <span className="text-foreground font-body">Professional certification prep</span>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works Section */}
        <section className="py-20 px-6 bg-secondary/30">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-4 font-heading">How it works</h2>
              <p className="text-xl text-muted-foreground font-body">Get started in three simple steps</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div className="bg-white p-8 rounded-xl text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mb-6">
                  <span className="text-xl font-bold text-primary font-heading">1</span>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-4 font-heading">Upload Your Materials</h3>
                <p className="text-muted-foreground font-body">
                  Simply upload your course materials, lecture notes, or any educational content you want to study.
                </p>
              </div>

              <div className="bg-white p-8 rounded-xl text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mb-6">
                  <span className="text-xl font-bold text-primary font-heading">2</span>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-4 font-heading">AI Processes Content</h3>
                <p className="text-muted-foreground font-body">
                  Our AI analyzes your materials and breaks them down into logical learning segments with personalized
                  questions.
                </p>
              </div>

              <div className="bg-white p-8 rounded-xl text-center">
                <div className="inline-flex items-center justify-center w-12 h-12 bg-primary/10 rounded-full mb-6">
                  <span className="text-xl font-bold text-primary font-heading">3</span>
                </div>
                <h3 className="text-xl font-semibold text-foreground mb-4 font-heading">Learn Through Dialogue</h3>
                <p className="text-muted-foreground font-body">
                  Engage with your AI tutor through Socratic questioning to achieve deep understanding and mastery.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" ref={pricingRef} className="py-20 px-6">
          {" "}
          {/* Attach the ref here */}
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-4 font-heading">Choose your plan</h2>
              <p className="text-xl text-muted-foreground font-body">Start free and upgrade as you learn</p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Free Plan */}
              <div className="group">
                <div className="bg-white p-8 rounded-xl border border-border transition-all duration-300 ease-in-out group-hover:translate-y-[-4px] group-hover:scale-[1.01] group-hover:shadow-lg">
                  <div className="mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-2 font-heading">Free</h3>
                    <div className="text-4xl font-bold text-foreground mb-2 font-heading">
                      0 <span className="text-lg font-normal text-muted-foreground font-body">FCFA</span>
                      <span className="text-lg font-normal text-muted-foreground font-body">/month</span>
                    </div>
                    <p className="text-muted-foreground font-body">Start learning today</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Limited chat messages</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Basic document uploads</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">AI tutoring sessions</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Progress tracking</span>
                    </li>
                  </ul>

                  <Button
                    asChild
                    variant="outline"
                    className="w-full border-border text-foreground hover:bg-accent bg-transparent font-body"
                  >
                    <Link href="/auth?mode=signup">
                      Start for Free <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              {/* Basic Plan - Most Popular */}
              <div className="group">
                <div className="bg-white p-8 rounded-xl border-2 border-primary relative transition-all duration-300 ease-in-out group-hover:translate-y-[-4px] group-hover:scale-[1.01] group-hover:shadow-lg">
                  <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                    <span className="bg-primary text-white px-4 py-1 rounded-full text-sm font-medium font-body">
                      Most Popular
                    </span>
                  </div>

                  <div className="mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-2 font-heading">Basic</h3>
                    <div className="text-4xl font-bold text-foreground mb-2 font-heading">
                      5,000 <span className="text-lg font-normal text-muted-foreground font-body">FCFA</span>
                      <span className="text-lg font-normal text-muted-foreground font-body">/month</span>
                    </div>
                    <p className="text-muted-foreground font-body">Unlock your full learning potential</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Unlimited chat messages</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Unlimited document uploads</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Advanced AI tutoring</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Detailed progress analytics</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Priority support</span>
                    </li>
                  </ul>

                  <Button asChild className="w-full bg-primary hover:bg-primary/90 text-white font-body">
                    <Link href="/auth?mode=signup">
                      Get Basic <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>

              {/* Pro Plan */}
              <div className="group">
                <div className="bg-white p-8 rounded-xl border border-border transition-all duration-300 ease-in-out group-hover:translate-y-[-4px] group-hover:scale-[1.01] group-hover:shadow-lg">
                  <div className="mb-8">
                    <h3 className="text-2xl font-bold text-foreground mb-2 font-heading">Pro</h3>
                    <div className="text-4xl font-bold text-foreground mb-2 font-heading">
                      10,000 <span className="text-lg font-normal text-muted-foreground font-body">FCFA</span>
                      <span className="text-lg font-normal text-muted-foreground font-body">/month</span>
                    </div>
                    <p className="text-muted-foreground font-body">For institutions and power users</p>
                  </div>

                  <ul className="space-y-4 mb-8">
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Everything in Basic</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Advanced AI models</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Custom learning paths</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">Team collaboration</span>
                    </li>
                    <li className="flex items-center gap-3">
                      <Check className="h-5 w-5 text-primary" />
                      <span className="text-foreground font-body">24/7 premium support</span>
                    </li>
                  </ul>

                  <Button
                    asChild
                    variant="outline"
                    className="w-full border-border text-foreground hover:bg-accent bg-transparent font-body"
                  >
                    <Link href="/auth?mode=signup">
                      Talk to us <ArrowRight className="ml-2 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-20 px-6">
          <div className="container mx-auto text-center max-w-4xl">
            <h2 className="text-4xl lg:text-5xl font-bold text-foreground mb-6 font-heading">
              Ready to transform your learning?
            </h2>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto font-body">
              Join thousands of students who are already mastering their courses with personalized AI tutoring.
            </p>
            <Button
              asChild
              size="lg"
              className="bg-primary hover:bg-primary/90 text-white text-lg px-8 py-4 rounded-lg font-body"
            >
              <Link href="/auth?mode=signup">
                Start Learning Today <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </Button>
          </div>
        </section>

        {/* Contact Section */}
        <section className="py-20 px-6 bg-secondary/30">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-16">
              <h2 className="text-4xl font-bold text-foreground mb-4 font-heading">Get in touch</h2>
              <p className="text-xl text-muted-foreground font-body">Have questions? We&apos;re here to help</p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Info */}
              <div className="space-y-8">
                <div className="flex items-start gap-4">
                  <div className="p-3 bg-white rounded-xl shadow-sm">
                    <Mail className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-foreground mb-2 font-heading">Email Support</h3>
                    <p className="text-muted-foreground mb-2 font-body">Get help from our support team</p>
                    <a
                      href="mailto:<EMAIL>"
                      className="text-primary hover:underline font-medium font-body"
                    >
                      <EMAIL>
                    </a>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 bg-white rounded-xl shadow-sm">
                    <Phone className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-foreground mb-2 font-heading">Phone Support</h3>
                    <p className="text-muted-foreground mb-2 font-body">Speak with our team directly</p>
                    <a href="tel:+1234567890" className="text-primary hover:underline font-medium font-body">
                      +1 (234) 567-890
                    </a>
                  </div>
                </div>

                <div className="flex items-start gap-4">
                  <div className="p-3 bg-white rounded-xl shadow-sm">
                    <MapPin className="h-6 w-6 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-semibold text-xl text-foreground mb-2 font-heading">Office Location</h3>
                    <p className="text-muted-foreground mb-2 font-body">Visit us in person</p>
                    <address className="not-italic text-primary font-body">
                      123 Learning Lane, Suite 400
                      <br />
                      Knowledge City, KL 98765
                    </address>
                  </div>
                </div>
              </div>

              {/* Contact Form */}
              <div className="bg-white p-8 rounded-xl border border-border">
                <h3 className="font-semibold text-2xl text-foreground mb-6 font-heading">Send us a message</h3>
                <form className="space-y-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name" className="text-foreground font-medium font-body">
                        Name
                      </Label>
                      <Input
                        id="name"
                        type="text"
                        placeholder="Your name"
                        className="mt-2 bg-white text-foreground border-border focus:ring-primary font-body"
                      />
                    </div>
                    <div>
                      <Label htmlFor="email" className="text-foreground font-medium font-body">
                        Email
                      </Label>
                      <Input
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        className="mt-2 bg-white text-foreground border-border focus:ring-primary font-body"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="subject" className="text-foreground font-medium font-body">
                      Subject
                    </Label>
                    <Input
                      id="subject"
                      type="text"
                      placeholder="How can we help?"
                      className="mt-2 bg-white text-foreground border-border focus:ring-primary font-body"
                    />
                  </div>
                  <div>
                    <Label htmlFor="message" className="text-foreground font-medium font-body">
                      Message
                    </Label>
                    <Textarea
                      id="message"
                      placeholder="Tell us more about your inquiry..."
                      rows={5}
                      className="mt-2 bg-white text-foreground border-border focus:ring-primary font-body"
                    />
                  </div>
                  <Button type="submit" className="w-full bg-primary hover:bg-primary/90 text-white font-body">
                    Send Message
                  </Button>
                </form>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="py-8 px-6 text-center text-muted-foreground text-sm border-t border-border bg-white">
        <div className="container mx-auto">
          <p className="font-body">&copy; {new Date().getFullYear()} Guided Tutor. All rights reserved.</p>
        </div>
      </footer>
    </div>
  )
}
