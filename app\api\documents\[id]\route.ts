import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"

export async function GET(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const supabase = await createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: documentId } = await params

    const document = await dbService.document.findUnique({
      where: { id: documentId },
      include: { chunks: true, progress: true }
    })

    if (!document || document.userId !== user.id) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 })
    }

    return NextResponse.json({ document })
  } catch (error) {
    console.error("Get document error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}

export async function DELETE(_request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const supabase = await createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { id: documentId } = await params

    // Verify ownership and get document with progress
    const document = await dbService.document.findFirst({
      where: {
        id: documentId,
        userId: user.id,
      },
      include: {
        progress: true
      }
    })

    if (!document) {
      return NextResponse.json({ error: "Document not found" }, { status: 404 })
    }

    // Clean up document-specific memories
    try {
      console.log(`🧹 Cleaning up memories for document: ${documentId}, user: ${user.id}`)

      // Import memory service dynamically to avoid circular dependencies
      const { memoryService } = await import('@/lib/ai/memory-service')

      // Delete all memories associated with this document
      const cleanupSuccess = await memoryService.deleteDocumentMemories(documentId, user.id)

      if (cleanupSuccess) {
        console.log(`✅ Successfully cleaned up memories for document: ${documentId}`)
      } else {
        console.warn(`⚠️ Some memories may not have been cleaned up for document: ${documentId}`)
      }
    } catch (memoryError) {
      console.error('⚠️ Failed to clean up memories:', memoryError)
      // Continue with deletion even if memory cleanup fails
    }

    // Delete file from Supabase Storage
    const filePath = `${user.id}/${document.filePath}`
    const { error: storageError } = await supabase.storage
      .from('documents')
      .remove([filePath])

    if (storageError) {
      console.error("Failed to delete file from storage:", storageError)
      // Continue with database deletion even if storage deletion fails
    }

    // Delete from database (cascades to chunks, messages, progress)
    await dbService.document.delete({
      where: { id: documentId },
    })

    console.log(`🗑️ Successfully deleted document ${documentId} and all associated data`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Delete document error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 })
  }
}
