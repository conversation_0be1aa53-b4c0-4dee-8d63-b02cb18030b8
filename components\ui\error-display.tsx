'use client'

import { <PERSON><PERSON><PERSON><PERSON>cle, RefreshCw, Wifi, WifiOff } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'

export interface ErrorDisplayProps {
  error: {
    type: string
    message: string
    technical?: string
    canRetry?: boolean
    provider?: string
  }
  onRetry?: () => void
  onDismiss?: () => void
  className?: string
}

export function ErrorDisplay({ 
  error, 
  onRetry, 
  onDismiss, 
  className = '' 
}: ErrorDisplayProps) {
  const getErrorIcon = () => {
    switch (error.type) {
      case 'NETWORK_ERROR':
      case 'TIMEOUT_ERROR':
        return <WifiOff className="h-4 w-4" />
      case 'PROVIDER_UNAVAILABLE':
      case 'API_RATE_LIMIT':
        return <Wifi className="h-4 w-4" />
      default:
        return <AlertCircle className="h-4 w-4" />
    }
  }

  const getErrorVariant = () => {
    switch (error.type) {
      case 'AUTHENTICATION_ERROR':
        return 'destructive'
      case 'PROVIDER_UNAVAILABLE':
      case 'API_RATE_LIMIT':
      case 'NETWORK_ERROR':
        return 'default'
      case 'MEMORY_SERVICE_ERROR':
        return 'default'
      default:
        return 'destructive'
    }
  }

  const getErrorTitle = () => {
    switch (error.type) {
      case 'PROVIDER_UNAVAILABLE':
        return 'AI Service Temporarily Unavailable'
      case 'API_RATE_LIMIT':
        return 'Rate Limit Exceeded'
      case 'AUTHENTICATION_ERROR':
        return 'Authentication Error'
      case 'MEMORY_SERVICE_ERROR':
        return 'Memory Service Issue'
      case 'NETWORK_ERROR':
        return 'Network Connection Issue'
      case 'TIMEOUT_ERROR':
        return 'Request Timed Out'
      default:
        return 'Something Went Wrong'
    }
  }

  return (
    <Alert variant={getErrorVariant()} className={className}>
      {getErrorIcon()}
      <AlertTitle>{getErrorTitle()}</AlertTitle>
      <AlertDescription className="mt-2">
        <div className="space-y-2">
          <p>{error.message}</p>
          
          {error.provider && (
            <p className="text-sm text-muted-foreground">
              Provider: {error.provider}
            </p>
          )}
          
          {error.technical && (
            <details className="text-sm">
              <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
                Technical details
              </summary>
              <p className="mt-1 font-mono text-xs bg-muted p-2 rounded">
                {error.technical}
              </p>
            </details>
          )}
          
          <div className="flex gap-2 mt-3">
            {error.canRetry && onRetry && (
              <Button 
                variant="outline" 
                size="sm" 
                onClick={onRetry}
                className="flex items-center gap-1"
              >
                <RefreshCw className="h-3 w-3" />
                Try Again
              </Button>
            )}
            
            {onDismiss && (
              <Button 
                variant="ghost" 
                size="sm" 
                onClick={onDismiss}
              >
                Dismiss
              </Button>
            )}
          </div>
        </div>
      </AlertDescription>
    </Alert>
  )
}

export function SystemStatusIndicator({ 
  status 
}: { 
  status: 'healthy' | 'degraded' | 'unavailable' | 'loading' 
}) {
  const getStatusColor = () => {
    switch (status) {
      case 'healthy':
        return 'bg-green-500'
      case 'degraded':
        return 'bg-yellow-500'
      case 'unavailable':
        return 'bg-red-500'
      case 'loading':
        return 'bg-gray-400 animate-pulse'
      default:
        return 'bg-gray-400'
    }
  }

  const getStatusText = () => {
    switch (status) {
      case 'healthy':
        return 'All systems operational'
      case 'degraded':
        return 'Some features may be limited'
      case 'unavailable':
        return 'Service temporarily unavailable'
      case 'loading':
        return 'Checking system status...'
      default:
        return 'Unknown status'
    }
  }

  return (
    <div className="flex items-center gap-2 text-sm text-muted-foreground">
      <div className={`w-2 h-2 rounded-full ${getStatusColor()}`} />
      <span>{getStatusText()}</span>
    </div>
  )
}
