import { type NextRequest, NextResponse } from "next/server"
import { createClient } from "@/lib/supabase/server"
import { dbService } from "@/lib/db-service"
import { llamaParseService } from "@/lib/llamaparse-service"
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/lib/error-handler"

export async function POST(request: NextRequest) {
    try {
        const supabase = await createClient()
        const {
            data: { session },
        } = await supabase.auth.getSession()

        if (!session) {
            return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
        }

        const body = await request.json()
        const { documentId } = body

        if (!documentId) {
            return NextResponse.json({ error: "Document ID is required" }, { status: 400 })
        }

        // Fetch document from database
        const document = await dbService.document.findFirst({
            where: {
                id: documentId,
                userId: session.user.id // Ensure user owns the document
            }
        })

        if (!document) {
            return NextResponse.json({ error: "Document not found" }, { status: 404 })
        }

        // Check if document is already processed
        if (document.status === 'READY') {
            return NextResponse.json({
                success: true,
                message: "Document already processed",
                status: 'READY',
                totalChunks: document.totalChunks
            })
        }

        // Update document status to PROCESSING
        await dbService.document.update({
            where: { id: documentId },
            data: {
                status: 'PROCESSING',
                errorMessage: null
            }
        })

        // Retrieve file from Supabase Storage
        const { data: fileData, error: downloadError } = await supabase.storage
            .from('documents')
            .download(`${session.user.id}/${document.filePath}`)

        if (downloadError || !fileData) {
            await dbService.document.update({
                where: { id: documentId },
                data: {
                    status: 'ERROR',
                    errorMessage: `Failed to retrieve file: ${downloadError?.message || 'File not found'}`
                }
            })
            return NextResponse.json({
                error: "Failed to retrieve file from storage",
                details: downloadError?.message
            }, { status: 500 })
        }

        // Convert Blob to Buffer for LlamaParse
        const arrayBuffer = await fileData.arrayBuffer()
        const buffer = Buffer.from(arrayBuffer)

        // Initialize LlamaParseReader and parse document
        console.log(`Starting LlamaParse processing for document ${documentId} (${document.fileName})`)

        let parsedDocuments
        try {
            parsedDocuments = await llamaParseService.parseDocumentFromBuffer(
                buffer,
                document.fileName
            )

            console.log(`LlamaParse successfully processed ${parsedDocuments.length} chunks for document ${documentId}`)

            // Log details about parsed chunks for debugging
            parsedDocuments.forEach((doc, index) => {
                console.log(`Chunk ${index}: ${doc.text.length} characters, metadata:`, doc.metadata)
            })

        } catch (parseError) {
            console.error(`LlamaParse parsing failed for document ${documentId}:`, parseError)

            // Use comprehensive error handling
            const processedError = await ErrorHandler.handleDocumentError(documentId, parseError, session.user.id)

            return NextResponse.json({
                error: "Document parsing failed",
                details: processedError.userMessage,
                retryable: processedError.retryable,
                code: processedError.code,
                documentId: document.id
            }, { status: 500 })
        }

        // Validate parsed documents
        if (!parsedDocuments || parsedDocuments.length === 0) {
            console.warn(`LlamaParse returned no chunks for document ${documentId}`)

            await dbService.document.update({
                where: { id: documentId },
                data: {
                    status: 'ERROR',
                    errorMessage: 'No content could be extracted from the document'
                }
            })

            return NextResponse.json({
                error: "No content extracted",
                details: "The document appears to be empty or in an unsupported format",
                documentId: document.id
            }, { status: 400 })
        }

        console.log(`Document ${documentId} successfully parsed into ${parsedDocuments.length} chunks`)

        try {
            // Store chunks in database
            console.log(`Storing ${parsedDocuments.length} chunks in database for document ${documentId}`)

            // Delete any existing chunks for this document (in case of reprocessing)
            await dbService.chunk.deleteMany({
                where: { documentId }
            })

            // Create chunks in database
            for (let i = 0; i < parsedDocuments.length; i++) {
                const chunk = parsedDocuments[i]
                const pageNumber = chunk.metadata.page_number || null

                await dbService.chunk.create({
                    data: {
                        documentId,
                        chunkIndex: i,
                        pageNumber,
                        content: chunk.text,
                        userId: session.user.id
                    }
                })
            }

            // Update document status to READY and set totalChunks
            await dbService.document.update({
                where: { id: documentId },
                data: {
                    status: 'READY',
                    totalChunks: parsedDocuments.length,
                    errorMessage: null
                }
            })

            console.log(`Successfully stored ${parsedDocuments.length} chunks for document ${documentId}`)

            return NextResponse.json({
                success: true,
                message: "Document processing completed successfully",
                documentId: document.id,
                status: 'READY',
                totalChunks: parsedDocuments.length
            })
        } catch (storageError) {
            console.error(`Failed to store chunks for document ${documentId}:`, storageError)

            // Update document status to ERROR
            await dbService.document.update({
                where: { id: documentId },
                data: {
                    status: 'ERROR',
                    errorMessage: `Failed to store document chunks: ${storageError instanceof Error ? storageError.message : 'Unknown error'}`
                }
            })

            return NextResponse.json({
                error: "Failed to store document chunks",
                details: storageError instanceof Error ? storageError.message : "Unknown error",
                documentId: document.id
            }, { status: 500 })
        }

    } catch (error) {
        console.error("Document processing error:", error)

        // Try to update document status to ERROR if we have documentId
        const body = await request.json().catch(() => ({}))
        if (body.documentId) {
            try {
                await dbService.document.update({
                    where: { id: body.documentId },
                    data: {
                        status: 'ERROR',
                        errorMessage: error instanceof Error ? error.message : 'Unknown processing error'
                    }
                })
            } catch (updateError) {
                console.error("Failed to update document status:", updateError)
            }
        }

        return NextResponse.json(
            {
                error: "Failed to process document",
                details: error instanceof Error ? error.message : "Unknown error",
            },
            { status: 500 }
        )
    }
}
