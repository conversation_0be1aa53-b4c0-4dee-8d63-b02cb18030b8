import { NextRequest, NextResponse } from 'next/server'
import { createClient } from '@/lib/supabase/server'
import { dbService } from '@/lib/db-service'

/**
 * GET /api/auth/last-document
 * Get the user's last active document for auto-redirect after login
 */
export async function GET(_request: NextRequest) {
  try {
    // Authenticate user
    const supabase = await createClient()
    const {
      data: { user },
      error: authError,
    } = await supabase.auth.getUser()

    if (authError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    // Get user's most recent document with progress
    const documents = await dbService.document.findMany({
      where: {
        userId: user.id
      },
      include: {
        progress: {
          where: {
            userId: user.id
          }
        }
      },
      orderBy: {
        updatedAt: 'desc' // Most recently updated first
      },
      take: 1
    })

    // Filter for ready documents
    const readyDocuments = documents.filter((doc: any) => doc.status === 'READY')

    if (readyDocuments.length === 0) {
      return NextResponse.json({
        hasLastDocument: false,
        message: 'No ready documents found'
      })
    }

    const lastDocument = readyDocuments[0]
    const progress = lastDocument.progress[0]

    return NextResponse.json({
      hasLastDocument: true,
      document: {
        id: lastDocument.id,
        name: lastDocument.fileName,
        currentChunk: progress?.currentChunk || 0,
        totalChunks: lastDocument.totalChunks,
        sessionId: progress?.sessionId
      }
    })

  } catch (error) {
    console.error('Last document API error:', error)
    return NextResponse.json(
      {
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
