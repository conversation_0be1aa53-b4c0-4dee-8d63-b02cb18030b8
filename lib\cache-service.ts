import { LRUCache } from 'lru-cache'

// TTL constants to avoid DRY violations
const DOCUMENT_TTL = 1000 * 60 * 60 * 24 // 24 hours
const CHAT_TTL = 1000 * 60 * 60 * 2 // 2 hours
const PROCESSING_TTL = 1000 * 60 * 10 // 10 minutes

export interface CacheEntry<T> {
    data: T
    timestamp: number
    ttl: number
}

export interface DocumentCache {
    chunks: Array<{
        chunkIndex: number
        content: string
        pageNumber?: number | null
    }>
    totalChunks: number
    status: 'READY' | 'PROCESSING' | 'ERROR'
    lastAccessed: number
}

export interface ChatCache {
    messages: Array<{
        role: 'user' | 'assistant'
        content: string
        timestamp: string
    }>
    currentChunk: number
    lastAccessed: number
}

export class CacheService {
    private documentCache: LRUCache<string, DocumentCache>
    private chatCache: LRUCache<string, ChatCache>
    private processingCache: LRUCache<string, { jobId: string; timestamp: number }>

    constructor() {
        // Document cache - stores processed document chunks
        this.documentCache = new LRUCache<string, DocumentCache>({
            max: 100, // Maximum 100 documents
            ttl: DOCUMENT_TTL,
            updateAgeOnGet: true,
            allowStale: false
        })

        // Chat cache - stores chat sessions
        this.chatCache = new LRUCache<string, ChatCache>({
            max: 500, // Maximum 500 chat sessions
            ttl: CHAT_TTL,
            updateAgeOnGet: true,
            allowStale: false
        })

        // Processing cache - prevents duplicate processing and stores job IDs
        this.processingCache = new LRUCache<string, { jobId: string; timestamp: number }>({
            max: 50, // Maximum 50 processing entries
            ttl: PROCESSING_TTL,
            updateAgeOnGet: false,
            allowStale: false
        })
    }

    /**
     * Document caching methods
     */

    getDocument(documentId: string): DocumentCache | undefined {
        const cached = this.documentCache.get(documentId)
        if (cached) {
            console.log(`[Cache] Document ${documentId} retrieved from cache`)
            // Return a copy to prevent external mutations
            return { ...cached, lastAccessed: Date.now() }
        }
        return undefined
    }
    setDocument(documentId: string, data: Omit<DocumentCache, 'lastAccessed'>): void {
        const cacheEntry: DocumentCache = {
            ...data,
            lastAccessed: Date.now()
        }
        this.documentCache.set(documentId, cacheEntry)
        console.log(`[Cache] Document ${documentId} stored in cache (${data.chunks.length} chunks)`)
    }

    invalidateDocument(documentId: string): void {
        this.documentCache.delete(documentId)
        console.log(`[Cache] Document ${documentId} invalidated`)
    }

    /**
     * Chat caching methods
     */

    getChat(sessionKey: string): ChatCache | undefined {
        const cached = this.chatCache.get(sessionKey)
        if (cached) {
            cached.lastAccessed = Date.now()
            console.log(`[Cache] Chat session ${sessionKey} retrieved from cache`)
        }
        return cached
    }

    setChat(sessionKey: string, data: Omit<ChatCache, 'lastAccessed'>): void {
        const cacheEntry: ChatCache = {
            ...data,
            lastAccessed: Date.now()
        }
        this.chatCache.set(sessionKey, cacheEntry)
        console.log(`[Cache] Chat session ${sessionKey} stored in cache`)
    }

    invalidateChat(sessionKey: string): void {
        this.chatCache.delete(sessionKey)
        console.log(`[Cache] Chat session ${sessionKey} invalidated`)
    }

    /**
     * Processing cache methods - prevents duplicate processing and tracks job IDs
     */

    isProcessing(documentId: string): boolean {
        return this.processingCache.has(documentId)
    }

    getProcessingJobId(documentId: string): string | undefined {
        const processingInfo = this.processingCache.get(documentId)
        return processingInfo?.jobId
    }

    setProcessing(documentId: string, jobId: string): void {
        this.processingCache.set(documentId, { jobId, timestamp: Date.now() })
        console.log(`[Cache] Document ${documentId} marked as processing with job ID ${jobId}`)
    }

    clearProcessing(documentId: string): void {
        this.processingCache.delete(documentId)
        console.log(`[Cache] Document ${documentId} processing flag cleared`)
    }

    /**
     * Cache statistics and management
     */

    getStats() {
        return {
            documents: {
                size: this.documentCache.size,
                max: this.documentCache.max,
                hits: this.documentCache.calculatedSize,
            },
            chats: {
                size: this.chatCache.size,
                max: this.chatCache.max,
                hits: this.chatCache.calculatedSize,
            },
            processing: {
                size: this.processingCache.size,
                max: this.processingCache.max,
            }
        }
    }

    clear(): void {
        this.documentCache.clear()
        this.chatCache.clear()
        this.processingCache.clear()
        console.log('[Cache] All caches cleared')
    }

    /**
     * Generate cache keys
     */

    static generateChatKey(userId: string, documentId: string): string {
        return `chat_${userId}_${documentId}`
    }

    static generateDocumentKey(documentId: string): string {
        return `doc_${documentId}`
    }

    /**
     * Manual cleanup for additional lastAccessed-based expiration
     * Note: LRU cache handles TTL automatically, this provides additional cleanup based on lastAccessed
     */
    cleanup(): void {
        const now = Date.now()
        let cleanedDocs = 0
        let cleanedChats = 0

        // Clean up old document cache entries based on lastAccessed
        this.documentCache.forEach((value, key) => {
            if (now - value.lastAccessed > DOCUMENT_TTL) {
                this.documentCache.delete(key)
                cleanedDocs++
            }
        })

        // Clean up old chat cache entries based on lastAccessed
        this.chatCache.forEach((value, key) => {
            if (now - value.lastAccessed > CHAT_TTL) {
                this.chatCache.delete(key)
                cleanedChats++
            }
        })

        if (cleanedDocs > 0 || cleanedChats > 0) {
            console.log(`[Cache] Manual cleanup completed: ${cleanedDocs} documents, ${cleanedChats} chats`)
        }
    }
}

// Singleton instance
export const cacheService = new CacheService()

// Cleanup every 30 minutes
setInterval(() => {
    cacheService.cleanup()
}, 30 * 60 * 1000)
